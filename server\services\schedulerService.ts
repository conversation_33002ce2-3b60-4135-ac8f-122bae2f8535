import cron from 'node-cron';
import { storage } from '../storage';
import { campaignService } from './campaignService';

class SchedulerService {
  private isRunning = false;

  start() {
    if (this.isRunning) return;

    // Check for scheduled campaigns every minute
    cron.schedule('* * * * *', async () => {
      try {
        await this.processScheduledCampaigns();
      } catch (error) {
        console.error('Error processing scheduled campaigns:', error);
      }
    });

    console.log('Scheduler service started');
    this.isRunning = true;
  }

  stop() {
    // Stop all cron jobs
    cron.getTasks().forEach(task => task.stop());
    this.isRunning = false;
    console.log('Scheduler service stopped');
  }

  private async processScheduledCampaigns() {
    try {
      // Get campaigns that are scheduled and due to start
      const scheduledCampaigns = await storage.getScheduledCampaigns();
      
      for (const campaign of scheduledCampaigns) {
        if (campaign.scheduledAt && new Date(campaign.scheduledAt) <= new Date()) {
          console.log(`Starting scheduled campaign: ${campaign.name} (${campaign.id})`);
          
          try {
            await campaignService.startCampaign(campaign.id);
            console.log(`Successfully started campaign: ${campaign.name}`);
          } catch (error) {
            console.error(`Error starting campaign ${campaign.id}:`, error);
            
            // Mark campaign as failed
            await storage.updateCampaign(campaign.id, {
              status: 'failed',
              completedAt: new Date(),
            });
          }
        }
      }
    } catch (error) {
      console.error('Error in processScheduledCampaigns:', error);
    }
  }

  // Method to schedule a campaign for a specific time
  async scheduleCampaign(campaignId: string, scheduledAt: Date): Promise<void> {
    await storage.updateCampaign(campaignId, {
      status: 'scheduled',
      scheduledAt,
    });

    console.log(`Campaign ${campaignId} scheduled for ${scheduledAt.toISOString()}`);
  }

  // Method to check if a campaign can be scheduled
  validateScheduleTime(scheduledAt: Date): boolean {
    const now = new Date();
    const oneMinuteFromNow = new Date(now.getTime() + 60000); // 1 minute buffer
    
    return scheduledAt >= oneMinuteFromNow;
  }

  // Method to get next scheduled campaigns for monitoring
  async getUpcomingCampaigns(limit: number = 10): Promise<any[]> {
    const scheduledCampaigns = await storage.getScheduledCampaigns();
    
    return scheduledCampaigns
      .filter(campaign => campaign.scheduledAt && new Date(campaign.scheduledAt) > new Date())
      .sort((a, b) => new Date(a.scheduledAt!).getTime() - new Date(b.scheduledAt!).getTime())
      .slice(0, limit)
      .map(campaign => ({
        id: campaign.id,
        name: campaign.name,
        scheduledAt: campaign.scheduledAt,
        totalContacts: campaign.totalContacts,
      }));
  }

  // Cleanup old completed campaigns (could be run daily)
  async cleanupOldCampaigns(daysOld: number = 30): Promise<void> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);

    try {
      // This would require a new storage method to find old campaigns
      // For now, just log the intention
      console.log(`Cleanup would remove campaigns older than ${cutoffDate.toISOString()}`);
    } catch (error) {
      console.error('Error during cleanup:', error);
    }
  }
}

export const schedulerService = new SchedulerService();
