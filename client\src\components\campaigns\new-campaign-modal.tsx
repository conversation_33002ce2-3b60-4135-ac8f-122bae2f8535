import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { z } from "zod";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import type { MessageTemplate, Contact, EvolutionInstance } from "@shared/schema";

const campaignSchema = z.object({
  name: z.string().min(1, "Campaign name is required"),
  templateId: z.string().min(1, "Please select a template"),
  evolutionInstanceId: z.string().min(1, "Please select an Evolution API instance"),
  minDelay: z.number().min(1, "Minimum delay must be at least 1 second"),
  maxDelay: z.number().min(1, "Maximum delay must be at least 1 second"),
  scheduledAt: z.string().optional(),
});

type CampaignFormData = z.infer<typeof campaignSchema>;

interface NewCampaignModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function NewCampaignModal({ isOpen, onClose }: NewCampaignModalProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [selectedContacts, setSelectedContacts] = useState<string[]>([]);
  const [showContactSelector, setShowContactSelector] = useState(false);

  const form = useForm<CampaignFormData>({
    resolver: zodResolver(campaignSchema),
    defaultValues: {
      name: "",
      templateId: "",
      evolutionInstanceId: "",
      minDelay: 5,
      maxDelay: 15,
      scheduledAt: "",
    },
  });

  const { data: templates } = useQuery<MessageTemplate[]>({
    queryKey: ["/api/templates"],
    enabled: isOpen,
  });

  const { data: instances } = useQuery<EvolutionInstance[]>({
    queryKey: ["/api/evolution-instances"],
    enabled: isOpen,
  });

  const { data: contacts } = useQuery<Contact[]>({
    queryKey: ["/api/contacts"],
    enabled: showContactSelector,
  });

  const createCampaignMutation = useMutation({
    mutationFn: async (data: CampaignFormData & { contactIds: string[] }) => {
      return await apiRequest("POST", "/api/campaigns", data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/campaigns"] });
      toast({ title: "Success", description: "Campaign created successfully" });
      onClose();
      form.reset();
      setSelectedContacts([]);
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const onSubmit = (data: CampaignFormData) => {
    if (selectedContacts.length === 0) {
      toast({
        title: "Error",
        description: "Please select at least one contact",
        variant: "destructive",
      });
      return;
    }

    if (data.minDelay >= data.maxDelay) {
      toast({
        title: "Error",
        description: "Maximum delay must be greater than minimum delay",
        variant: "destructive",
      });
      return;
    }

    const scheduleDate = data.scheduledAt ? new Date(data.scheduledAt) : null;
    const status = scheduleDate && scheduleDate > new Date() ? "scheduled" : "running";

    createCampaignMutation.mutate({
      ...data,
      contactIds: selectedContacts,
      status,
      scheduledAt: scheduleDate?.toISOString(),
    } as any);
  };

  const selectedTemplate = templates?.find(t => t.id === form.watch("templateId"));
  const availableVariables = selectedTemplate?.variables as string[] || [];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create New Campaign</DialogTitle>
        </DialogHeader>

        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Campaign Details */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <Label htmlFor="name">Campaign Name</Label>
              <Input
                id="name"
                {...form.register("name")}
                placeholder="Enter campaign name"
                data-testid="input-campaign-name"
              />
              {form.formState.errors.name && (
                <p className="text-sm text-destructive mt-1">{form.formState.errors.name.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="evolutionInstanceId">Evolution API Instance</Label>
              <select
                id="evolutionInstanceId"
                {...form.register("evolutionInstanceId")}
                className="w-full px-3 py-2 border border-border rounded-md bg-input focus:ring-2 focus:ring-ring focus:border-transparent"
                data-testid="select-evolution-instance"
              >
                <option value="">Select API Instance</option>
                {instances?.filter(i => i.status === "connected").map(instance => (
                  <option key={instance.id} value={instance.id}>
                    {instance.name}
                  </option>
                ))}
              </select>
              {form.formState.errors.evolutionInstanceId && (
                <p className="text-sm text-destructive mt-1">{form.formState.errors.evolutionInstanceId.message}</p>
              )}
            </div>
          </div>

          {/* Template Selection */}
          <div>
            <Label htmlFor="templateId">Message Template</Label>
            <select
              id="templateId"
              {...form.register("templateId")}
              className="w-full px-3 py-2 border border-border rounded-md bg-input focus:ring-2 focus:ring-ring focus:border-transparent"
              data-testid="select-template"
            >
              <option value="">Select a template</option>
              {templates?.map(template => (
                <option key={template.id} value={template.id}>
                  {template.name}
                </option>
              ))}
            </select>
            {form.formState.errors.templateId && (
              <p className="text-sm text-destructive mt-1">{form.formState.errors.templateId.message}</p>
            )}

            {selectedTemplate && (
              <Card className="mt-4">
                <CardContent className="p-4">
                  <div className="space-y-3">
                    <div>
                      <h4 className="font-medium text-card-foreground mb-2">Template Preview</h4>
                      <div className="p-3 bg-muted rounded-md border">
                        <p className="text-sm text-card-foreground whitespace-pre-wrap">
                          {selectedTemplate.content}
                        </p>
                      </div>
                    </div>
                    
                    {availableVariables.length > 0 && (
                      <div>
                        <h4 className="font-medium text-card-foreground mb-2">Available Variables</h4>
                        <div className="flex flex-wrap gap-2">
                          {availableVariables.map(variable => (
                            <Badge key={variable} className="bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300">
                              {`{${variable}}`}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Timing Configuration */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <Label>Random Delay Between Messages</Label>
              <div className="grid grid-cols-2 gap-3 mt-2">
                <div>
                  <Label htmlFor="minDelay" className="text-xs text-muted-foreground">Min (seconds)</Label>
                  <Input
                    id="minDelay"
                    type="number"
                    min="1"
                    {...form.register("minDelay", { valueAsNumber: true })}
                    data-testid="input-min-delay"
                  />
                </div>
                <div>
                  <Label htmlFor="maxDelay" className="text-xs text-muted-foreground">Max (seconds)</Label>
                  <Input
                    id="maxDelay"
                    type="number"
                    min="1"
                    {...form.register("maxDelay", { valueAsNumber: true })}
                    data-testid="input-max-delay"
                  />
                </div>
              </div>
              <p className="text-xs text-muted-foreground mt-1">Random delay helps avoid spam detection</p>
            </div>

            <div>
              <Label htmlFor="scheduledAt">Schedule (Optional)</Label>
              <Input
                id="scheduledAt"
                type="datetime-local"
                {...form.register("scheduledAt")}
                min={new Date().toISOString().slice(0, 16)}
                data-testid="input-schedule-time"
              />
            </div>
          </div>

          {/* Contact Selection */}
          <div>
            <Label>Select Contacts</Label>
            <Card className="mt-2">
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-4">
                    <Button 
                      type="button"
                      onClick={() => window.location.href = "/contacts"}
                      data-testid="button-import-contacts"
                    >
                      <i className="fas fa-file-upload mr-2"></i>
                      Import from Excel
                    </Button>
                    <Button 
                      type="button" 
                      variant="outline"
                      onClick={() => setShowContactSelector(!showContactSelector)}
                      data-testid="button-select-contacts"
                    >
                      <i className="fas fa-address-book mr-2"></i>
                      Select from Contacts
                    </Button>
                  </div>
                  <span className="text-sm text-muted-foreground">
                    Selected: <span className="font-medium text-card-foreground">{selectedContacts.length}</span> contacts
                  </span>
                </div>

                {showContactSelector && (
                  <div className="border border-border rounded-md max-h-60 overflow-y-auto">
                    {contacts?.map(contact => (
                      <div key={contact.id} className="flex items-center p-3 border-b border-border hover:bg-accent">
                        <input
                          type="checkbox"
                          checked={selectedContacts.includes(contact.id)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedContacts([...selectedContacts, contact.id]);
                            } else {
                              setSelectedContacts(selectedContacts.filter(id => id !== contact.id));
                            }
                          }}
                          className="mr-3"
                          data-testid={`checkbox-contact-${contact.id}`}
                        />
                        <div className="flex-1">
                          <p className="font-medium text-card-foreground">
                            {contact.firstName || contact.lastName 
                              ? `${contact.firstName || ''} ${contact.lastName || ''}`.trim()
                              : contact.phoneNumber
                            }
                          </p>
                          <p className="text-sm text-muted-foreground">{contact.phoneNumber}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                )}

                {selectedContacts.length === 0 && !showContactSelector && (
                  <div className="border border-border rounded-md p-8 bg-muted min-h-[100px] flex items-center justify-center text-muted-foreground">
                    <div className="text-center">
                      <i className="fas fa-users text-2xl mb-2"></i>
                      <p>No contacts selected yet</p>
                      <p className="text-xs">Import from Excel or select from your contact list</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center justify-between pt-6 border-t border-border">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <div className="space-x-3">
              <Button 
                type="submit" 
                disabled={createCampaignMutation.isPending}
                data-testid="button-create-campaign"
              >
                {createCampaignMutation.isPending ? "Creating..." : "Create Campaign"}
              </Button>
            </div>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
