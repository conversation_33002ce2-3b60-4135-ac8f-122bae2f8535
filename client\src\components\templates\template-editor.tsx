import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { z } from "zod";
import { <PERSON><PERSON>, <PERSON>alogContent, <PERSON><PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import type { MessageTemplate } from "@shared/schema";

const templateSchema = z.object({
  name: z.string().min(1, "Template name is required"),
  content: z.string().min(1, "Message content is required"),
  category: z.string().optional(),
});

type TemplateFormData = z.infer<typeof templateSchema>;

interface TemplateEditorProps {
  isOpen: boolean;
  onClose: () => void;
  template?: MessageTemplate | null;
}

export default function TemplateEditor({ isOpen, onClose, template }: TemplateEditorProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const form = useForm<TemplateFormData>({
    resolver: zodResolver(templateSchema),
    defaultValues: {
      name: template?.name || "",
      content: template?.content || "",
      category: template?.category || "",
    },
  });

  const saveTemplateMutation = useMutation({
    mutationFn: async (data: TemplateFormData & { variables: string[] }) => {
      if (template) {
        return await apiRequest("PUT", `/api/templates/${template.id}`, data);
      } else {
        return await apiRequest("POST", "/api/templates", data);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/templates"] });
      toast({ 
        title: "Success", 
        description: template ? "Template updated successfully" : "Template created successfully" 
      });
      onClose();
      form.reset();
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const onSubmit = (data: TemplateFormData) => {
    // Extract variables from content
    const variables = extractVariables(data.content);
    saveTemplateMutation.mutate({ ...data, variables });
  };

  const extractVariables = (content: string): string[] => {
    const matches = content.match(/{([^}]+)}/g);
    return matches ? Array.from(new Set(matches.map(match => match.replace(/[{}]/g, '')))) : [];
  };

  const insertVariable = (variable: string) => {
    const currentContent = form.getValues("content");
    const cursorPosition = 0; // In a real implementation, you'd track cursor position
    const newContent = currentContent + `{${variable}}`;
    form.setValue("content", newContent);
  };

  const availableVariables = [
    "firstName",
    "lastName", 
    "email",
    "company",
    "phoneNumber",
  ];

  const currentVariables = extractVariables(form.watch("content"));

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {template ? "Edit Template" : "Create New Template"}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Template Details */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <Label htmlFor="name">Template Name</Label>
              <Input
                id="name"
                {...form.register("name")}
                placeholder="Enter template name"
                data-testid="input-template-name"
              />
              {form.formState.errors.name && (
                <p className="text-sm text-destructive mt-1">{form.formState.errors.name.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="category">Category (Optional)</Label>
              <Input
                id="category"
                {...form.register("category")}
                placeholder="e.g., Marketing, Support, Notification"
                data-testid="input-template-category"
              />
            </div>
          </div>

          {/* Message Content */}
          <div>
            <Label htmlFor="content">Message Content</Label>
            
            {/* Variable Helper */}
            <div className="mt-2 mb-4 p-3 border border-border rounded-md bg-muted">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-card-foreground">Available Variables</span>
                <span className="text-xs text-muted-foreground">Click to insert</span>
              </div>
              <div className="flex flex-wrap gap-2">
                {availableVariables.map((variable) => (
                  <Button
                    key={variable}
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => insertVariable(variable)}
                    className="text-xs"
                    data-testid={`button-insert-${variable}`}
                  >
                    {`{${variable}}`}
                  </Button>
                ))}
              </div>
            </div>

            <Textarea
              id="content"
              {...form.register("content")}
              rows={8}
              placeholder="Enter your message template here. Use variables like {firstName} to personalize messages."
              className="resize-none"
              data-testid="textarea-template-content"
            />
            {form.formState.errors.content && (
              <p className="text-sm text-destructive mt-1">{form.formState.errors.content.message}</p>
            )}

            {/* Variables in Use */}
            {currentVariables.length > 0 && (
              <div className="mt-3">
                <span className="text-sm font-medium text-card-foreground">Variables in use:</span>
                <div className="flex flex-wrap gap-2 mt-2">
                  {currentVariables.map((variable) => (
                    <Badge key={variable} className="bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300">
                      {`{${variable}}`}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Preview */}
          <div>
            <Label>Preview</Label>
            <div className="mt-2 p-4 border border-border rounded-md bg-muted">
              <p className="text-sm text-card-foreground whitespace-pre-wrap">
                {form.watch("content") || "Your message preview will appear here..."}
              </p>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-between pt-6 border-t border-border">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button 
              type="submit" 
              disabled={saveTemplateMutation.isPending}
              data-testid="button-save-template"
            >
              {saveTemplateMutation.isPending 
                ? "Saving..." 
                : template ? "Update Template" : "Create Template"
              }
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
