import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import TemplateEditor from "@/components/templates/template-editor";
import { isUnauthorizedError } from "@/lib/authUtils";
import type { MessageTemplate } from "@shared/schema";

export default function Templates() {
  const { toast } = useToast();
  const { isAuthenticated, isLoading } = useAuth();
  const [showEditor, setShowEditor] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<MessageTemplate | null>(null);

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      toast({
        title: "Unauthorized",
        description: "You are logged out. Logging in again...",
        variant: "destructive",
      });
      setTimeout(() => {
        window.location.href = "/api/login";
      }, 500);
      return;
    }
  }, [isAuthenticated, isLoading, toast]);

  const { data: templates, isLoading: templatesLoading } = useQuery<MessageTemplate[]>({
    queryKey: ["/api/templates"],
    enabled: isAuthenticated,
  });

  const handleNewTemplate = () => {
    setEditingTemplate(null);
    setShowEditor(true);
  };

  const handleEditTemplate = (template: MessageTemplate) => {
    setEditingTemplate(template);
    setShowEditor(true);
  };

  const getVariablesFromContent = (content: string): string[] => {
    const matches = content.match(/{([^}]+)}/g);
    return matches ? matches.map(match => match.replace(/[{}]/g, '')) : [];
  };

  if (isLoading || templatesLoading) {
    return (
      <div className="flex-1 overflow-y-auto bg-background p-6">
        <div className="animate-pulse space-y-6">
          <div className="flex justify-between items-center">
            <div className="h-8 bg-muted rounded w-48"></div>
            <div className="h-10 bg-muted rounded w-32"></div>
          </div>
          <div className="grid gap-6">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-40 bg-muted rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="flex-1 overflow-y-auto bg-background p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-bold text-card-foreground">Message Templates</h2>
          <p className="text-muted-foreground">Create and manage message templates with variables</p>
        </div>
        <Button onClick={handleNewTemplate} data-testid="button-new-template">
          <i className="fas fa-plus mr-2"></i>
          New Template
        </Button>
      </div>

      <div className="grid gap-6">
        {templates?.map((template) => {
          const variables = getVariablesFromContent(template.content);
          
          return (
            <Card key={template.id} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">{template.name}</CardTitle>
                  <div className="flex items-center space-x-2">
                    {template.category && (
                      <Badge variant="outline">{template.category}</Badge>
                    )}
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => handleEditTemplate(template)}
                      data-testid={`button-edit-template-${template.id}`}
                    >
                      <i className="fas fa-edit mr-1"></i>
                      Edit
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-card-foreground mb-2">Message Content</h4>
                    <div className="p-3 bg-muted rounded-md border">
                      <p className="text-sm text-card-foreground whitespace-pre-wrap">
                        {template.content}
                      </p>
                    </div>
                  </div>

                  {variables.length > 0 && (
                    <div>
                      <h4 className="font-medium text-card-foreground mb-2">Variables Used</h4>
                      <div className="flex flex-wrap gap-2">
                        {variables.map((variable) => (
                          <Badge key={variable} className="bg-blue-100 text-blue-800">
                            {`{${variable}}`}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}

                  <div className="pt-4 border-t border-border">
                    <div className="flex items-center justify-between text-sm text-muted-foreground">
                      <span>Created: {new Date(template.createdAt).toLocaleDateString()}</span>
                      <span>Last updated: {new Date(template.updatedAt).toLocaleDateString()}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}

        {templates?.length === 0 && (
          <Card>
            <CardContent className="text-center py-12">
              <i className="fas fa-file-alt text-4xl text-muted-foreground mb-4"></i>
              <h3 className="text-lg font-semibold text-card-foreground mb-2">No Templates Yet</h3>
              <p className="text-muted-foreground mb-4">
                Create your first message template to use in campaigns.
              </p>
              <Button onClick={handleNewTemplate}>
                <i className="fas fa-plus mr-2"></i>
                Create Template
              </Button>
            </CardContent>
          </Card>
        )}
      </div>

      {showEditor && (
        <TemplateEditor 
          isOpen={showEditor} 
          onClose={() => setShowEditor(false)}
          template={editingTemplate}
        />
      )}
    </div>
  );
}
