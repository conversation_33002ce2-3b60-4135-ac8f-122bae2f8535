import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import NewCampaignModal from "@/components/campaigns/new-campaign-modal";
import CampaignProgress from "@/components/campaigns/campaign-progress";
import { isUnauthorizedError } from "@/lib/authUtils";
import type { Campaign } from "@shared/schema";

export default function Campaigns() {
  const { toast } = useToast();
  const { isAuthenticated, isLoading } = useAuth();
  const [showNewCampaign, setShowNewCampaign] = useState(false);

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      toast({
        title: "Unauthorized",
        description: "You are logged out. Logging in again...",
        variant: "destructive",
      });
      setTimeout(() => {
        window.location.href = "/api/login";
      }, 500);
      return;
    }
  }, [isAuthenticated, isLoading, toast]);

  const { data: campaigns, isLoading: campaignsLoading } = useQuery<Campaign[]>({
    queryKey: ["/api/campaigns"],
    enabled: isAuthenticated,
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'bg-green-500';
      case 'paused': return 'bg-orange-500';
      case 'completed': return 'bg-blue-500';
      case 'failed': return 'bg-red-500';
      case 'cancelled': return 'bg-gray-500';
      case 'scheduled': return 'bg-purple-500';
      default: return 'bg-gray-500';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'running': return 'Running';
      case 'paused': return 'Paused';
      case 'completed': return 'Completed';
      case 'failed': return 'Failed';
      case 'cancelled': return 'Cancelled';
      case 'scheduled': return 'Scheduled';
      case 'draft': return 'Draft';
      default: return status;
    }
  };

  if (isLoading || campaignsLoading) {
    return (
      <div className="flex-1 overflow-y-auto bg-background p-6">
        <div className="animate-pulse space-y-6">
          <div className="flex justify-between items-center">
            <div className="h-8 bg-muted rounded w-48"></div>
            <div className="h-10 bg-muted rounded w-32"></div>
          </div>
          <div className="grid gap-6">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-40 bg-muted rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="flex-1 overflow-y-auto bg-background p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-bold text-card-foreground">Campaigns</h2>
          <p className="text-muted-foreground">Manage your WhatsApp messaging campaigns</p>
        </div>
        <Button 
          onClick={() => setShowNewCampaign(true)}
          data-testid="button-new-campaign"
        >
          <i className="fas fa-plus mr-2"></i>
          New Campaign
        </Button>
      </div>

      <div className="grid gap-6">
        {campaigns?.map((campaign) => (
          <Card key={campaign.id} className="hover:shadow-md transition-shadow">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">{campaign.name}</CardTitle>
                <Badge className={`text-white ${getStatusColor(campaign.status)}`}>
                  {getStatusLabel(campaign.status)}
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-card-foreground">{campaign.totalContacts}</div>
                  <div className="text-sm text-muted-foreground">Total Contacts</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{campaign.sentCount}</div>
                  <div className="text-sm text-muted-foreground">Sent</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{campaign.deliveredCount}</div>
                  <div className="text-sm text-muted-foreground">Delivered</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">{campaign.failedCount}</div>
                  <div className="text-sm text-muted-foreground">Failed</div>
                </div>
              </div>

              {campaign.status === 'running' || campaign.status === 'paused' ? (
                <CampaignProgress campaignId={campaign.id} />
              ) : null}

              {campaign.scheduledAt && (
                <div className="mt-4 p-3 bg-muted rounded-md">
                  <div className="flex items-center space-x-2 text-sm">
                    <i className="fas fa-calendar text-muted-foreground"></i>
                    <span className="text-muted-foreground">Scheduled for:</span>
                    <span className="font-medium">
                      {new Date(campaign.scheduledAt).toLocaleString()}
                    </span>
                  </div>
                </div>
              )}

              <div className="mt-4 pt-4 border-t border-border">
                <div className="flex items-center justify-between text-sm text-muted-foreground">
                  <span>Created: {new Date(campaign.createdAt).toLocaleDateString()}</span>
                  {campaign.startedAt && (
                    <span>Started: {new Date(campaign.startedAt).toLocaleString()}</span>
                  )}
                  {campaign.completedAt && (
                    <span>Completed: {new Date(campaign.completedAt).toLocaleString()}</span>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}

        {campaigns?.length === 0 && (
          <Card>
            <CardContent className="text-center py-12">
              <i className="fas fa-bullhorn text-4xl text-muted-foreground mb-4"></i>
              <h3 className="text-lg font-semibold text-card-foreground mb-2">No Campaigns Yet</h3>
              <p className="text-muted-foreground mb-4">
                Create your first WhatsApp messaging campaign to get started.
              </p>
              <Button onClick={() => setShowNewCampaign(true)}>
                <i className="fas fa-plus mr-2"></i>
                Create Campaign
              </Button>
            </CardContent>
          </Card>
        )}
      </div>

      {showNewCampaign && (
        <NewCampaignModal 
          isOpen={showNewCampaign} 
          onClose={() => setShowNewCampaign(false)}
        />
      )}
    </div>
  );
}
