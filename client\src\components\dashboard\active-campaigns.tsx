import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { useWebSocket } from "@/hooks/useWebSocket";
import { useEffect } from "react";
import type { Campaign } from "@shared/schema";

export default function ActiveCampaigns() {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: campaigns, isLoading } = useQuery<Campaign[]>({
    queryKey: ["/api/campaigns/active"],
    retry: false,
  });

  const pauseCampaignMutation = useMutation({
    mutationFn: async (campaignId: string) => {
      await apiRequest("POST", `/api/campaigns/${campaignId}/pause`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/campaigns/active"] });
      queryClient.invalidateQueries({ queryKey: ["/api/campaigns"] });
      toast({ title: "Success", description: "Campaign paused successfully" });
    },
    onError: (error) => {
      toast({ 
        title: "Error", 
        description: error.message,
        variant: "destructive" 
      });
    },
  });

  const resumeCampaignMutation = useMutation({
    mutationFn: async (campaignId: string) => {
      await apiRequest("POST", `/api/campaigns/${campaignId}/resume`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/campaigns/active"] });
      queryClient.invalidateQueries({ queryKey: ["/api/campaigns"] });
      toast({ title: "Success", description: "Campaign resumed successfully" });
    },
    onError: (error) => {
      toast({ 
        title: "Error", 
        description: error.message,
        variant: "destructive" 
      });
    },
  });

  const stopCampaignMutation = useMutation({
    mutationFn: async (campaignId: string) => {
      await apiRequest("POST", `/api/campaigns/${campaignId}/stop`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/campaigns/active"] });
      queryClient.invalidateQueries({ queryKey: ["/api/campaigns"] });
      toast({ title: "Success", description: "Campaign stopped successfully" });
    },
    onError: (error) => {
      toast({ 
        title: "Error", 
        description: error.message,
        variant: "destructive" 
      });
    },
  });

  // WebSocket for real-time updates
  useWebSocket({
    onMessage: (message) => {
      if (message.type === 'campaign_progress') {
        queryClient.invalidateQueries({ queryKey: ["/api/campaigns/active"] });
        queryClient.invalidateQueries({ queryKey: ["/api/campaigns"] });
      }
    },
  });

  const getStatusColor = (status: string | null) => {
    switch (status) {
      case "running": return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300";
      case "paused": return "bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-300";
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300";
    }
  };

  const calculateProgress = (campaign: Campaign) => {
    if ((campaign.totalContacts || 0) === 0) return 0;
    return Math.round(((campaign.sentCount || 0) / (campaign.totalContacts || 1)) * 100);
  };

  const formatTime = (date: string | Date | null) => {
    if (!date) return "";
    return new Date(date).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Active Campaigns</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            {[...Array(2)].map((_, i) => (
              <div key={i} className="h-32 bg-muted rounded-lg"></div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Active Campaigns</CardTitle>
          <Button variant="outline" size="sm" onClick={() => window.location.href = "/campaigns"}>
            View All
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {campaigns?.map((campaign) => {
            const progress = calculateProgress(campaign);
            
            return (
              <div key={campaign.id} className="border border-border rounded-lg p-4 hover:bg-accent/50 transition-colors">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <div className={`w-3 h-3 rounded-full ${
                      campaign.status === "running" ? "bg-green-500 pulse-green" : "bg-orange-500"
                    }`}></div>
                    <h4 className="font-medium text-card-foreground">{campaign.name}</h4>
                  </div>
                  <Badge className={getStatusColor(campaign.status || "unknown")}>
                    {campaign.status === "running" ? "Running" : "Paused"}
                  </Badge>
                </div>
                
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Progress</span>
                    <span className="font-medium text-card-foreground">
                      {progress}% ({campaign.sentCount}/{campaign.totalContacts})
                    </span>
                  </div>
                  <div className="w-full bg-muted rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full progress-bar ${
                        campaign.status === "running" ? "bg-green-500" : "bg-orange-500"
                      }`}
                      style={{ width: `${progress}%` }}
                    ></div>
                  </div>
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>
                      {campaign.status === "running" ? "Started" : "Paused"}: {formatTime(campaign.startedAt || campaign.pausedAt)}
                    </span>
                    <span>ETA: Manual</span>
                  </div>
                </div>
                
                <div className="flex space-x-2 mt-3">
                  {campaign.status === "running" ? (
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => pauseCampaignMutation.mutate(campaign.id)}
                      disabled={pauseCampaignMutation.isPending}
                      data-testid={`button-pause-campaign-${campaign.id}`}
                    >
                      <i className="fas fa-pause mr-1"></i>Pause
                    </Button>
                  ) : (
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => resumeCampaignMutation.mutate(campaign.id)}
                      disabled={resumeCampaignMutation.isPending}
                      data-testid={`button-resume-campaign-${campaign.id}`}
                    >
                      <i className="fas fa-play mr-1"></i>Resume
                    </Button>
                  )}
                  <Button 
                    size="sm" 
                    variant="outline"
                    onClick={() => stopCampaignMutation.mutate(campaign.id)}
                    disabled={stopCampaignMutation.isPending}
                    data-testid={`button-stop-campaign-${campaign.id}`}
                  >
                    <i className="fas fa-stop mr-1"></i>Stop
                  </Button>
                </div>
              </div>
            );
          })}

          {campaigns?.length === 0 && (
            <div className="text-center py-8">
              <i className="fas fa-bullhorn text-4xl text-muted-foreground mb-4"></i>
              <h3 className="text-lg font-semibold text-card-foreground mb-2">No Active Campaigns</h3>
              <p className="text-muted-foreground">Create a new campaign to get started.</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
