import * as XLSX from 'xlsx';
import { type InsertContact } from '@shared/schema';

interface ContactRow {
  phoneNumber: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  company?: string;
  [key: string]: any; // For custom fields
}

class ExcelService {
  generateContactTemplate(): Buffer {
    // Create a sample template with headers and example data
    const templateData = [
      {
        phoneNumber: '+1234567890',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        company: 'Example Corp',
        customField1: 'Custom Value 1',
        customField2: 'Custom Value 2',
      }
    ];

    // Create workbook and worksheet
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.json_to_sheet(templateData);

    // Add headers
    const headers = [
      'phoneNumber (Required)',
      'firstName',
      'lastName', 
      'email',
      'company',
      'customField1',
      'customField2'
    ];

    XLSX.utils.sheet_add_aoa(worksheet, [headers], { origin: 'A1' });

    // Style the header row
    const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1');
    for (let col = range.s.c; col <= range.e.c; col++) {
      const cellRef = XLSX.utils.encode_cell({ r: 0, c: col });
      if (worksheet[cellRef]) {
        worksheet[cellRef].s = {
          font: { bold: true },
          fill: { fgColor: { rgb: "EEEEEE" } }
        };
      }
    }

    // Set column widths
    worksheet['!cols'] = [
      { width: 20 }, // phoneNumber
      { width: 15 }, // firstName
      { width: 15 }, // lastName
      { width: 25 }, // email
      { width: 20 }, // company
      { width: 15 }, // customField1
      { width: 15 }, // customField2
    ];

    // Add the worksheet to workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Contacts');

    // Add instructions sheet
    const instructions = [
      ['WhatsApp Bulk Messaging - Contact Import Template'],
      [''],
      ['Instructions:'],
      ['1. Fill in the contact information in the "Contacts" sheet'],
      ['2. phoneNumber is required and should include country code (e.g., +1234567890)'],
      ['3. Other fields are optional but help with message personalization'],
      ['4. You can add custom fields by adding new columns'],
      ['5. Save the file and upload it in the application'],
      [''],
      ['Supported Variables for Message Templates:'],
      ['{firstName} - Contact\'s first name'],
      ['{lastName} - Contact\'s last name'],
      ['{email} - Contact\'s email address'],
      ['{company} - Contact\'s company name'],
      ['{phoneNumber} - Contact\'s phone number'],
      ['{customField1} - Custom field 1 value'],
      ['{customField2} - Custom field 2 value'],
      [''],
      ['Note: Variables in message templates will be replaced with actual values for each contact'],
    ];

    const instructionsSheet = XLSX.utils.aoa_to_sheet(instructions);
    instructionsSheet['!cols'] = [{ width: 70 }];
    XLSX.utils.book_append_sheet(workbook, instructionsSheet, 'Instructions');

    // Convert to buffer
    return XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
  }

  async parseContactsFromExcel(buffer: Buffer, userId: string): Promise<InsertContact[]> {
    try {
      // Read the workbook
      const workbook = XLSX.read(buffer, { type: 'buffer' });
      
      // Get the first worksheet (or specifically 'Contacts' sheet)
      let worksheet = workbook.Sheets['Contacts'] || workbook.Sheets[workbook.SheetNames[0]];
      
      if (!worksheet) {
        throw new Error('No valid worksheet found');
      }

      // Convert to JSON
      const rawData = XLSX.utils.sheet_to_json<any>(worksheet, { header: 1 });
      
      if (rawData.length < 2) {
        throw new Error('Excel file must contain at least a header row and one data row');
      }

      // Get headers from first row
      const headers = rawData[0] as string[];
      const dataRows = rawData.slice(1);

      // Find required phoneNumber column
      const phoneNumberIndex = this.findColumnIndex(headers, ['phoneNumber', 'phone', 'number']);
      if (phoneNumberIndex === -1) {
        throw new Error('phoneNumber column is required. Please include a column named "phoneNumber"');
      }

      // Find optional columns
      const firstNameIndex = this.findColumnIndex(headers, ['firstName', 'first_name', 'first']);
      const lastNameIndex = this.findColumnIndex(headers, ['lastName', 'last_name', 'last']);
      const emailIndex = this.findColumnIndex(headers, ['email', 'email_address']);
      const companyIndex = this.findColumnIndex(headers, ['company', 'organization', 'org']);

      const contacts: InsertContact[] = [];

      for (let i = 0; i < dataRows.length; i++) {
        const row = dataRows[i] as any[];
        
        // Skip empty rows
        if (!row || row.length === 0 || !row[phoneNumberIndex]) {
          continue;
        }

        const phoneNumber = this.cleanPhoneNumber(row[phoneNumberIndex]);
        
        if (!phoneNumber) {
          console.warn(`Row ${i + 2}: Invalid phone number: ${row[phoneNumberIndex]}`);
          continue;
        }

        // Build custom fields from additional columns
        const customFields: Record<string, any> = {};
        headers.forEach((header, index) => {
          if (index !== phoneNumberIndex && 
              index !== firstNameIndex && 
              index !== lastNameIndex && 
              index !== emailIndex && 
              index !== companyIndex &&
              row[index] !== undefined && 
              row[index] !== null && 
              row[index] !== '') {
            customFields[header] = row[index];
          }
        });

        const contact: InsertContact = {
          phoneNumber,
          firstName: firstNameIndex !== -1 ? row[firstNameIndex]?.toString().trim() : undefined,
          lastName: lastNameIndex !== -1 ? row[lastNameIndex]?.toString().trim() : undefined,
          email: emailIndex !== -1 ? row[emailIndex]?.toString().trim() : undefined,
          company: companyIndex !== -1 ? row[companyIndex]?.toString().trim() : undefined,
          customFields: Object.keys(customFields).length > 0 ? customFields : undefined,
          userId,
        };

        contacts.push(contact);
      }

      if (contacts.length === 0) {
        throw new Error('No valid contacts found in the Excel file');
      }

      return contacts;

    } catch (error) {
      console.error('Error parsing Excel file:', error);
      throw new Error(`Failed to parse Excel file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private findColumnIndex(headers: string[], possibleNames: string[]): number {
    const lowerHeaders = headers.map(h => h?.toLowerCase().trim());
    const lowerPossibleNames = possibleNames.map(n => n.toLowerCase());
    
    for (const name of lowerPossibleNames) {
      const index = lowerHeaders.findIndex(h => h.includes(name));
      if (index !== -1) {
        return index;
      }
    }
    
    return -1;
  }

  private cleanPhoneNumber(phone: any): string | null {
    if (!phone) return null;
    
    // Convert to string and remove all non-numeric characters except +
    let cleaned = phone.toString().replace(/[^\d+]/g, '');
    
    // If no + at start, add it
    if (!cleaned.startsWith('+')) {
      cleaned = '+' + cleaned;
    }

    // Validate: should have + followed by 10-15 digits
    const phoneRegex = /^\+\d{10,15}$/;
    if (!phoneRegex.test(cleaned)) {
      return null;
    }

    return cleaned;
  }

  async exportCampaignResults(campaignId: string): Promise<Buffer> {
    // This could be implemented to export campaign results
    // For now, return a simple workbook
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.aoa_to_sheet([['Campaign Results Export - Not Implemented']]);
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Results');
    
    return XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
  }
}

export const excelService = new ExcelService();
