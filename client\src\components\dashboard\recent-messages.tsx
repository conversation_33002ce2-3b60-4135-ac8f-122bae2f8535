import { useQuery } from "@tanstack/react-query";
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useWebSocket } from "@/hooks/useWebSocket";
import { useQueryClient } from "@tanstack/react-query";
import type { CampaignMessage } from "@shared/schema";

export default function RecentMessages() {
  const queryClient = useQueryClient();

  const { data: messages, isLoading } = useQuery<CampaignMessage[]>({
    queryKey: ["/api/dashboard/recent-messages"],
    retry: false,
  });

  // WebSocket for real-time updates
  useWebSocket({
    onMessage: (message) => {
      if (message.type === 'message_update') {
        queryClient.invalidateQueries({ queryKey: ["/api/dashboard/recent-messages"] });
      }
    },
  });

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "delivered": return { icon: "fas fa-check", color: "bg-green-100 text-green-600 dark:bg-green-900/20 dark:text-green-400" };
      case "sent": return { icon: "fas fa-paper-plane", color: "bg-blue-100 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400" };
      case "pending": return { icon: "fas fa-clock", color: "bg-orange-100 text-orange-600 dark:bg-orange-900/20 dark:text-orange-400" };
      case "sending": return { icon: "fas fa-spinner", color: "bg-blue-100 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400" };
      case "failed": return { icon: "fas fa-times", color: "bg-red-100 text-red-600 dark:bg-red-900/20 dark:text-red-400" };
      default: return { icon: "fas fa-clock", color: "bg-gray-100 text-gray-600 dark:bg-gray-900/20 dark:text-gray-400" };
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "delivered": return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300";
      case "sent": return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300";
      case "pending": return "bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-300";
      case "sending": return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300";
      case "failed": return "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300";
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300";
    }
  };

  const formatTimeAgo = (date: string) => {
    const now = new Date();
    const messageDate = new Date(date);
    const diffMs = now.getTime() - messageDate.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return "Just now";
    if (diffMins < 60) return `${diffMins} minutes ago`;
    if (diffMins < 1440) return `${Math.floor(diffMins / 60)} hours ago`;
    return `${Math.floor(diffMins / 1440)} days ago`;
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case "delivered": return "Delivered";
      case "sent": return "Sent";
      case "pending": return "Pending";
      case "sending": return "Sending";
      case "failed": return "Failed";
      default: return "Unknown";
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Recent Messages</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-16 bg-muted rounded-lg"></div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Recent Messages</CardTitle>
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-500 rounded-full pulse-green"></div>
            <span className="text-xs text-muted-foreground">Live Updates</span>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3 max-h-80 overflow-y-auto">
          {messages?.map((message) => {
            const statusIcon = getStatusIcon(message.status);
            
            return (
              <div key={message.id} className="flex items-center justify-between p-3 border border-border rounded-lg hover:bg-accent/50 transition-colors">
                <div className="flex items-center space-x-3">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${statusIcon.color}`}>
                    <i className={`${statusIcon.icon} text-xs`}></i>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-card-foreground">{message.phoneNumber}</p>
                    <p className="text-xs text-muted-foreground">
                      {formatTimeAgo(message.createdAt)}
                    </p>
                  </div>
                </div>
                <Badge className={getStatusColor(message.status)}>
                  {getStatusLabel(message.status)}
                </Badge>
              </div>
            );
          })}

          {messages?.length === 0 && (
            <div className="text-center py-8">
              <i className="fas fa-comments text-4xl text-muted-foreground mb-4"></i>
              <h3 className="text-lg font-semibold text-card-foreground mb-2">No Recent Messages</h3>
              <p className="text-muted-foreground">Messages will appear here when campaigns are active.</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
