import { useLocation } from "wouter";
import { useAuth } from "@/hooks/useAuth";

export default function Sidebar() {
  const [location, setLocation] = useLocation();
  const { user } = useAuth();

  const navigation = [
    { name: "Dashboard", href: "/", icon: "fas fa-chart-line" },
    { name: "Campaigns", href: "/campaigns", icon: "fas fa-bullhorn" },
    { name: "Contacts", href: "/contacts", icon: "fas fa-address-book" },
    { name: "Templates", href: "/templates", icon: "fas fa-file-alt" },
    { name: "API Status", href: "/api-status", icon: "fas fa-plug" },
    { name: "Settings", href: "/settings", icon: "fas fa-cog" },
  ];

  const isActive = (href: string) => {
    if (href === "/") {
      return location === "/";
    }
    return location.startsWith(href);
  };

  return (
    <div className="w-64 bg-sidebar shadow-lg border-r border-sidebar-border">
      <div className="p-6 border-b border-sidebar-border">
        <div className="flex items-center space-x-3">
          <i className="fab fa-whatsapp text-3xl text-green-500"></i>
          <div>
            <h1 className="text-xl font-bold text-sidebar-foreground">WhatsApp Bulk</h1>
            <p className="text-sm text-muted-foreground">Messaging Platform</p>
          </div>
        </div>
      </div>
      
      <nav className="p-4">
        <ul className="space-y-2">
          {navigation.map((item) => (
            <li key={item.name}>
              <button
                onClick={() => setLocation(item.href)}
                className={`w-full flex items-center space-x-3 px-3 py-2 rounded-md transition-colors text-left ${
                  isActive(item.href)
                    ? "bg-sidebar-primary text-sidebar-primary-foreground"
                    : "text-muted-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
                }`}
                data-testid={`nav-${item.name.toLowerCase().replace(" ", "-")}`}
              >
                <i className={item.icon}></i>
                <span>{item.name}</span>
              </button>
            </li>
          ))}
        </ul>
      </nav>

      <div className="absolute bottom-0 w-64 p-4 border-t border-sidebar-border bg-sidebar">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <img 
              src={user?.profileImageUrl || "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=40&h=40"} 
              alt="User avatar" 
              className="w-8 h-8 rounded-full object-cover"
            />
            <div>
              <p className="text-sm font-medium text-sidebar-foreground">
                {user?.firstName || user?.email || "User"}
              </p>
              <p className="text-xs text-muted-foreground">
                {user?.role || "Agent"}
              </p>
            </div>
          </div>
          <button 
            className="text-muted-foreground hover:text-destructive"
            onClick={() => window.location.href = "/api/logout"}
            data-testid="button-logout"
          >
            <i className="fas fa-sign-out-alt"></i>
          </button>
        </div>
      </div>
    </div>
  );
}
