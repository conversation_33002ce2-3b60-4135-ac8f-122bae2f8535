import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import ContactImport from "@/components/contacts/contact-import";
import { isUnauthorizedError } from "@/lib/authUtils";
import type { Contact, ContactGroup } from "@shared/schema";

export default function Contacts() {
  const { toast } = useToast();
  const { isAuthenticated, isLoading } = useAuth();
  const [showImport, setShowImport] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedGroup, setSelectedGroup] = useState<string>("");

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      toast({
        title: "Unauthorized",
        description: "You are logged out. Logging in again...",
        variant: "destructive",
      });
      setTimeout(() => {
        window.location.href = "/api/login";
      }, 500);
      return;
    }
  }, [isAuthenticated, isLoading, toast]);

  const { data: contacts, isLoading: contactsLoading } = useQuery<Contact[]>({
    queryKey: ["/api/contacts", selectedGroup],
    enabled: isAuthenticated,
  });

  const { data: groups } = useQuery<ContactGroup[]>({
    queryKey: ["/api/contact-groups"],
    enabled: isAuthenticated,
  });

  const filteredContacts = contacts?.filter(contact => {
    if (!searchTerm) return true;
    const search = searchTerm.toLowerCase();
    return (
      contact.phoneNumber.toLowerCase().includes(search) ||
      contact.firstName?.toLowerCase().includes(search) ||
      contact.lastName?.toLowerCase().includes(search) ||
      contact.email?.toLowerCase().includes(search) ||
      contact.company?.toLowerCase().includes(search)
    );
  });

  const downloadTemplate = () => {
    window.open('/api/contacts/template', '_blank');
  };

  if (isLoading || contactsLoading) {
    return (
      <div className="flex-1 overflow-y-auto bg-background p-6">
        <div className="animate-pulse space-y-6">
          <div className="flex justify-between items-center">
            <div className="h-8 bg-muted rounded w-48"></div>
            <div className="flex space-x-2">
              <div className="h-10 bg-muted rounded w-32"></div>
              <div className="h-10 bg-muted rounded w-32"></div>
            </div>
          </div>
          <div className="grid gap-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-20 bg-muted rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="flex-1 overflow-y-auto bg-background p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-bold text-card-foreground">Contacts</h2>
          <p className="text-muted-foreground">Manage your contact lists</p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={downloadTemplate} data-testid="button-download-template">
            <i className="fas fa-download mr-2"></i>
            Download Template
          </Button>
          <Button onClick={() => setShowImport(true)} data-testid="button-import-contacts">
            <i className="fas fa-file-upload mr-2"></i>
            Import Contacts
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Filters Sidebar */}
        <Card className="lg:col-span-1">
          <CardHeader>
            <CardTitle className="text-lg">Filters</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-card-foreground mb-2">Search</label>
              <Input
                placeholder="Search contacts..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                data-testid="input-search-contacts"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-card-foreground mb-2">Group</label>
              <select 
                className="w-full px-3 py-2 border border-border rounded-md bg-input focus:ring-2 focus:ring-ring focus:border-transparent"
                value={selectedGroup}
                onChange={(e) => setSelectedGroup(e.target.value)}
                data-testid="select-contact-group"
              >
                <option value="">All Groups</option>
                {groups?.map(group => (
                  <option key={group.id} value={group.id}>
                    {group.name}
                  </option>
                ))}
              </select>
            </div>

            <div className="pt-4 border-t border-border">
              <h4 className="font-medium text-card-foreground mb-2">Summary</h4>
              <div className="space-y-1 text-sm text-muted-foreground">
                <div>Total: {contacts?.length || 0}</div>
                <div>Filtered: {filteredContacts?.length || 0}</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Contacts List */}
        <div className="lg:col-span-3">
          {filteredContacts?.length === 0 && !contactsLoading ? (
            <Card>
              <CardContent className="text-center py-12">
                <i className="fas fa-address-book text-4xl text-muted-foreground mb-4"></i>
                <h3 className="text-lg font-semibold text-card-foreground mb-2">
                  {searchTerm ? 'No Matching Contacts' : 'No Contacts Yet'}
                </h3>
                <p className="text-muted-foreground mb-4">
                  {searchTerm 
                    ? 'Try adjusting your search criteria or filters.'
                    : 'Import contacts from Excel or add them manually to get started.'
                  }
                </p>
                {!searchTerm && (
                  <div className="space-x-2">
                    <Button onClick={() => setShowImport(true)}>
                      <i className="fas fa-file-upload mr-2"></i>
                      Import Contacts
                    </Button>
                    <Button variant="outline" onClick={downloadTemplate}>
                      <i className="fas fa-download mr-2"></i>
                      Download Template
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {filteredContacts?.map((contact) => (
                <Card key={contact.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                            <i className="fas fa-user text-primary"></i>
                          </div>
                          <div>
                            <h3 className="font-medium text-card-foreground">
                              {contact.firstName || contact.lastName 
                                ? `${contact.firstName || ''} ${contact.lastName || ''}`.trim()
                                : 'Unknown Contact'
                              }
                            </h3>
                            <p className="text-sm text-muted-foreground">{contact.phoneNumber}</p>
                          </div>
                        </div>
                        
                        <div className="mt-3 grid grid-cols-1 md:grid-cols-3 gap-2 text-sm">
                          {contact.email && (
                            <div className="flex items-center space-x-2">
                              <i className="fas fa-envelope text-muted-foreground"></i>
                              <span className="text-muted-foreground">{contact.email}</span>
                            </div>
                          )}
                          {contact.company && (
                            <div className="flex items-center space-x-2">
                              <i className="fas fa-building text-muted-foreground"></i>
                              <span className="text-muted-foreground">{contact.company}</span>
                            </div>
                          )}
                          <div className="flex items-center space-x-2">
                            <i className="fas fa-calendar text-muted-foreground"></i>
                            <span className="text-muted-foreground">
                              Added {new Date(contact.createdAt).toLocaleDateString()}
                            </span>
                          </div>
                        </div>

                        {contact.customFields && Object.keys(contact.customFields).length > 0 && (
                          <div className="mt-3 flex flex-wrap gap-1">
                            {Object.entries(contact.customFields).map(([key, value]) => (
                              <Badge key={key} variant="outline" className="text-xs">
                                {key}: {String(value)}
                              </Badge>
                            ))}
                          </div>
                        )}
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <Button variant="outline" size="sm" data-testid={`button-edit-contact-${contact.id}`}>
                          <i className="fas fa-edit"></i>
                        </Button>
                        <Button variant="outline" size="sm" data-testid={`button-delete-contact-${contact.id}`}>
                          <i className="fas fa-trash text-red-500"></i>
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      </div>

      {showImport && (
        <ContactImport 
          isOpen={showImport} 
          onClose={() => setShowImport(false)}
        />
      )}
    </div>
  );
}
