import { sql } from 'drizzle-orm';
import {
  index,
  jsonb,
  pgTable,
  timestamp,
  varchar,
  text,
  integer,
  boolean,
  decimal,
  pgEnum,
} from "drizzle-orm/pg-core";
import { relations } from 'drizzle-orm';
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

// Session storage table for Replit Auth
export const sessions = pgTable(
  "sessions",
  {
    sid: varchar("sid").primaryKey(),
    sess: jsonb("sess").notNull(),
    expire: timestamp("expire").notNull(),
  },
  (table) => [index("IDX_session_expire").on(table.expire)],
);

// User storage table for Replit Auth
export const users = pgTable("users", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  email: varchar("email").unique(),
  firstName: varchar("first_name"),
  lastName: varchar("last_name"),
  profileImageUrl: varchar("profile_image_url"),
  role: varchar("role").default("agent"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Evolution API Instances
export const evolutionInstances = pgTable("evolution_instances", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  name: varchar("name").notNull(),
  instanceKey: varchar("instance_key").notNull().unique(),
  evolutionUrl: varchar("evolution_url").notNull(),
  apiKey: varchar("api_key").notNull(),
  status: varchar("status").default("disconnected"),
  userId: varchar("user_id").references(() => users.id),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Contact Groups
export const contactGroups = pgTable("contact_groups", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  name: varchar("name").notNull(),
  description: text("description"),
  userId: varchar("user_id").references(() => users.id),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Contacts
export const contacts = pgTable("contacts", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  phoneNumber: varchar("phone_number").notNull(),
  firstName: varchar("first_name"),
  lastName: varchar("last_name"),
  email: varchar("email"),
  company: varchar("company"),
  customFields: jsonb("custom_fields"),
  groupId: varchar("group_id").references(() => contactGroups.id),
  userId: varchar("user_id").references(() => users.id),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Message Templates
export const messageTemplates = pgTable("message_templates", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  name: varchar("name").notNull(),
  content: text("content").notNull(),
  variables: jsonb("variables"), // Array of variable names like ["firstName", "company"]
  category: varchar("category"),
  userId: varchar("user_id").references(() => users.id),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Campaign Status Enum
export const campaignStatusEnum = pgEnum("campaign_status", [
  "draft",
  "scheduled",
  "running",
  "paused",
  "completed",
  "cancelled",
  "failed"
]);

// Campaigns
export const campaigns = pgTable("campaigns", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  name: varchar("name").notNull(),
  templateId: varchar("template_id").references(() => messageTemplates.id),
  evolutionInstanceId: varchar("evolution_instance_id").references(() => evolutionInstances.id),
  status: campaignStatusEnum("status").default("draft"),
  scheduledAt: timestamp("scheduled_at"),
  startedAt: timestamp("started_at"),
  completedAt: timestamp("completed_at"),
  pausedAt: timestamp("paused_at"),
  minDelay: integer("min_delay").default(5), // seconds
  maxDelay: integer("max_delay").default(15), // seconds
  totalContacts: integer("total_contacts").default(0),
  sentCount: integer("sent_count").default(0),
  deliveredCount: integer("delivered_count").default(0),
  failedCount: integer("failed_count").default(0),
  userId: varchar("user_id").references(() => users.id),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Message Status Enum
export const messageStatusEnum = pgEnum("message_status", [
  "pending",
  "sending",
  "sent",
  "delivered",
  "read",
  "failed"
]);

// Campaign Messages
export const campaignMessages = pgTable("campaign_messages", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  campaignId: varchar("campaign_id").references(() => campaigns.id),
  contactId: varchar("contact_id").references(() => contacts.id),
  phoneNumber: varchar("phone_number").notNull(),
  messageContent: text("message_content").notNull(),
  status: messageStatusEnum("status").default("pending"),
  scheduledAt: timestamp("scheduled_at"),
  sentAt: timestamp("sent_at"),
  deliveredAt: timestamp("delivered_at"),
  readAt: timestamp("read_at"),
  errorMessage: text("error_message"),
  evolutionMessageId: varchar("evolution_message_id"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Relations
export const usersRelations = relations(users, ({ many }) => ({
  evolutionInstances: many(evolutionInstances),
  contactGroups: many(contactGroups),
  contacts: many(contacts),
  messageTemplates: many(messageTemplates),
  campaigns: many(campaigns),
}));

export const evolutionInstancesRelations = relations(evolutionInstances, ({ one, many }) => ({
  user: one(users, { fields: [evolutionInstances.userId], references: [users.id] }),
  campaigns: many(campaigns),
}));

export const contactGroupsRelations = relations(contactGroups, ({ one, many }) => ({
  user: one(users, { fields: [contactGroups.userId], references: [users.id] }),
  contacts: many(contacts),
}));

export const contactsRelations = relations(contacts, ({ one, many }) => ({
  user: one(users, { fields: [contacts.userId], references: [users.id] }),
  group: one(contactGroups, { fields: [contacts.groupId], references: [contactGroups.id] }),
  campaignMessages: many(campaignMessages),
}));

export const messageTemplatesRelations = relations(messageTemplates, ({ one, many }) => ({
  user: one(users, { fields: [messageTemplates.userId], references: [users.id] }),
  campaigns: many(campaigns),
}));

export const campaignsRelations = relations(campaigns, ({ one, many }) => ({
  user: one(users, { fields: [campaigns.userId], references: [users.id] }),
  template: one(messageTemplates, { fields: [campaigns.templateId], references: [messageTemplates.id] }),
  evolutionInstance: one(evolutionInstances, { fields: [campaigns.evolutionInstanceId], references: [evolutionInstances.id] }),
  campaignMessages: many(campaignMessages),
}));

export const campaignMessagesRelations = relations(campaignMessages, ({ one }) => ({
  campaign: one(campaigns, { fields: [campaignMessages.campaignId], references: [campaigns.id] }),
  contact: one(contacts, { fields: [campaignMessages.contactId], references: [contacts.id] }),
}));

// Insert Schemas
export const upsertUserSchema = createInsertSchema(users);
export const insertEvolutionInstanceSchema = createInsertSchema(evolutionInstances).omit({ id: true, createdAt: true, updatedAt: true });
export const insertContactGroupSchema = createInsertSchema(contactGroups).omit({ id: true, createdAt: true, updatedAt: true });
export const insertContactSchema = createInsertSchema(contacts).omit({ id: true, createdAt: true, updatedAt: true });
export const insertMessageTemplateSchema = createInsertSchema(messageTemplates).omit({ id: true, createdAt: true, updatedAt: true });
export const insertCampaignSchema = createInsertSchema(campaigns).omit({ id: true, createdAt: true, updatedAt: true });
export const insertCampaignMessageSchema = createInsertSchema(campaignMessages).omit({ id: true, createdAt: true, updatedAt: true });

// Types
export type UpsertUser = z.infer<typeof upsertUserSchema>;
export type User = typeof users.$inferSelect;
export type EvolutionInstance = typeof evolutionInstances.$inferSelect;
export type InsertEvolutionInstance = z.infer<typeof insertEvolutionInstanceSchema>;
export type ContactGroup = typeof contactGroups.$inferSelect;
export type InsertContactGroup = z.infer<typeof insertContactGroupSchema>;
export type Contact = typeof contacts.$inferSelect;
export type InsertContact = z.infer<typeof insertContactSchema>;
export type MessageTemplate = typeof messageTemplates.$inferSelect;
export type InsertMessageTemplate = z.infer<typeof insertMessageTemplateSchema>;
export type Campaign = typeof campaigns.$inferSelect;
export type InsertCampaign = z.infer<typeof insertCampaignSchema>;
export type CampaignMessage = typeof campaignMessages.$inferSelect;
export type InsertCampaignMessage = z.infer<typeof insertCampaignMessageSchema>;
