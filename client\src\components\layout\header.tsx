import { useLocation } from "wouter";
import { Button } from "@/components/ui/button";
import { useQuery } from "@tanstack/react-query";

export default function Header() {
  const [location] = useLocation();

  const { data: instances } = useQuery({
    queryKey: ["/api/evolution-instances"],
    retry: false,
  });

  const getPageTitle = () => {
    switch (location) {
      case "/": return "Dashboard";
      case "/campaigns": return "Campaigns";
      case "/contacts": return "Contacts";
      case "/templates": return "Message Templates";
      case "/api-status": return "API Status";
      case "/settings": return "Settings";
      default: return "Dashboard";
    }
  };

  const getPageDescription = () => {
    switch (location) {
      case "/": return "Monitor your WhatsApp messaging campaigns";
      case "/campaigns": return "Manage your WhatsApp messaging campaigns";
      case "/contacts": return "Manage your contact lists";
      case "/templates": return "Create and manage message templates";
      case "/api-status": return "Monitor Evolution API connections";
      case "/settings": return "Configure your account settings";
      default: return "Monitor your WhatsApp messaging campaigns";
    }
  };

  const isConnected = instances?.some(instance => instance.status === "connected");

  return (
    <header className="bg-card shadow-sm border-b border-border p-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-card-foreground">{getPageTitle()}</h2>
          <p className="text-muted-foreground">{getPageDescription()}</p>
        </div>
        <div className="flex items-center space-x-4">
          <div className={`flex items-center space-x-2 px-3 py-2 border rounded-md ${
            isConnected 
              ? "bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800" 
              : "bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800"
          }`}>
            <div className={`w-2 h-2 rounded-full ${
              isConnected ? "bg-green-500 pulse-green" : "bg-red-500"
            }`}></div>
            <span className={`text-sm font-medium ${
              isConnected ? "text-green-700 dark:text-green-300" : "text-red-700 dark:text-red-300"
            }`}>
              Evolution API {isConnected ? "Connected" : "Disconnected"}
            </span>
          </div>
          {location === "/" && (
            <Button 
              onClick={() => window.location.href = "/campaigns"}
              data-testid="button-new-campaign"
            >
              <i className="fas fa-plus mr-2"></i>
              New Campaign
            </Button>
          )}
        </div>
      </div>
    </header>
  );
}
