import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useQuery } from "@tanstack/react-query";
import { Badge } from "@/components/ui/badge";
import type { Campaign } from "@shared/schema";

export default function QuickActions() {
  const { data: scheduledCampaigns } = useQuery<Campaign[]>({
    queryKey: ["/api/campaigns"],
    select: (campaigns) => campaigns?.filter(c => c.status === "scheduled") || [],
    retry: false,
  });

  const quickActions = [
    {
      title: "Import Contacts",
      description: "Upload Excel file with phone numbers",
      icon: "fas fa-file-excel",
      color: "blue",
      action: () => window.location.href = "/contacts",
    },
    {
      title: "Create Template",
      description: "Design message templates with variables",
      icon: "fas fa-file-alt",
      color: "green",
      action: () => window.location.href = "/templates",
    },
    {
      title: "Schedule Campaign",
      description: "Plan your messaging campaigns",
      icon: "fas fa-calendar-alt",
      color: "purple",
      action: () => window.location.href = "/campaigns",
    },
    {
      title: "Download Template",
      description: "Get Excel template for contacts",
      icon: "fas fa-download",
      color: "orange",
      action: () => window.open('/api/contacts/template', '_blank'),
    },
  ];

  const getColorClasses = (color: string) => {
    switch (color) {
      case "blue": return "bg-blue-100 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400";
      case "green": return "bg-green-100 text-green-600 dark:bg-green-900/20 dark:text-green-400";
      case "purple": return "bg-purple-100 text-purple-600 dark:bg-purple-900/20 dark:text-purple-400";
      case "orange": return "bg-orange-100 text-orange-600 dark:bg-orange-900/20 dark:text-orange-400";
      default: return "bg-gray-100 text-gray-600 dark:bg-gray-900/20 dark:text-gray-400";
    }
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <Card className="lg:col-span-2">
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {quickActions.map((action, index) => (
              <Button
                key={index}
                variant="outline"
                className="h-auto p-4 justify-start text-left hover:bg-accent"
                onClick={action.action}
                data-testid={`quick-action-${action.title.toLowerCase().replace(/\s+/g, '-')}`}
              >
                <div className="flex items-center space-x-4 w-full">
                  <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${getColorClasses(action.color)}`}>
                    <i className={`${action.icon} text-xl`}></i>
                  </div>
                  <div className="flex-1 text-left">
                    <h4 className="font-medium text-card-foreground">{action.title}</h4>
                    <p className="text-sm text-muted-foreground">{action.description}</p>
                  </div>
                </div>
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Upcoming Scheduled Campaigns */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Scheduled</CardTitle>
            <i className="fas fa-clock text-muted-foreground"></i>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {scheduledCampaigns?.slice(0, 3).map((campaign) => (
              <div key={campaign.id} className="border border-border rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-card-foreground text-sm">{campaign.name}</h4>
                  <Badge className="bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300">
                    Scheduled
                  </Badge>
                </div>
                <div className="space-y-1">
                  <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                    <i className="fas fa-calendar"></i>
                    <span>
                      {campaign.scheduledAt 
                        ? new Date(campaign.scheduledAt).toLocaleString()
                        : "Not scheduled"
                      }
                    </span>
                  </div>
                  <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                    <i className="fas fa-users"></i>
                    <span>{campaign.totalContacts} contacts</span>
                  </div>
                </div>
                <div className="flex space-x-2 mt-3">
                  <Button 
                    size="sm" 
                    variant="outline"
                    onClick={() => window.location.href = "/campaigns"}
                    data-testid={`button-edit-scheduled-${campaign.id}`}
                  >
                    Edit
                  </Button>
                  <Button 
                    size="sm" 
                    variant="outline"
                    data-testid={`button-cancel-scheduled-${campaign.id}`}
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            ))}

            {scheduledCampaigns?.length === 0 && (
              <div className="text-center py-8">
                <i className="fas fa-calendar-alt text-2xl text-muted-foreground mb-2"></i>
                <p className="text-sm text-muted-foreground">No scheduled campaigns</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
