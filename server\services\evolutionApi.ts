import axios, { AxiosInstance } from 'axios';

interface EvolutionApiConfig {
  baseUrl: string;
  apiKey: string;
  instanceKey: string;
}

interface SendMessagePayload {
  number: string;
  text: string;
}

interface MessageResponse {
  success: boolean;
  messageId?: string;
  error?: string;
}

interface InstanceStatus {
  state: string;
  status: string;
}

class EvolutionApiService {
  private createClient(config: EvolutionApiConfig): AxiosInstance {
    return axios.create({
      baseURL: config.baseUrl,
      headers: {
        'Content-Type': 'application/json',
        'apikey': config.apiKey,
      },
      timeout: 30000,
    });
  }

  async testConnection(baseUrl: string, apiKey: string, instanceKey: string): Promise<boolean> {
    try {
      const client = this.createClient({ baseUrl, apiKey, instanceKey });
      const response = await client.get(`/instance/fetchInstances`);
      return response.status === 200;
    } catch (error) {
      console.error('Evolution API connection test failed:', error);
      return false;
    }
  }

  async getInstanceStatus(config: EvolutionApiConfig): Promise<InstanceStatus | null> {
    try {
      const client = this.createClient(config);
      const response = await client.get(`/instance/connectionState/${config.instanceKey}`);
      return response.data;
    } catch (error) {
      console.error('Error getting instance status:', error);
      return null;
    }
  }

  async sendMessage(config: EvolutionApiConfig, payload: SendMessagePayload): Promise<MessageResponse> {
    try {
      const client = this.createClient(config);
      const response = await client.post(`/message/sendText/${config.instanceKey}`, payload);
      
      if (response.status === 200 || response.status === 201) {
        return {
          success: true,
          messageId: response.data?.key?.id || response.data?.messageId,
        };
      } else {
        return {
          success: false,
          error: response.data?.message || 'Unknown error',
        };
      }
    } catch (error: any) {
      console.error('Error sending message:', error);
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Failed to send message',
      };
    }
  }

  async createInstance(config: EvolutionApiConfig): Promise<boolean> {
    try {
      const client = this.createClient(config);
      const response = await client.post('/instance/create', {
        instanceName: config.instanceKey,
        token: config.apiKey,
        qrcode: true,
        webhook: true,
      });
      return response.status === 200 || response.status === 201;
    } catch (error) {
      console.error('Error creating instance:', error);
      return false;
    }
  }

  async deleteInstance(config: EvolutionApiConfig): Promise<boolean> {
    try {
      const client = this.createClient(config);
      const response = await client.delete(`/instance/delete/${config.instanceKey}`);
      return response.status === 200;
    } catch (error) {
      console.error('Error deleting instance:', error);
      return false;
    }
  }

  async getQRCode(config: EvolutionApiConfig): Promise<string | null> {
    try {
      const client = this.createClient(config);
      const response = await client.get(`/instance/qrcode/${config.instanceKey}`);
      return response.data?.qrcode || null;
    } catch (error) {
      console.error('Error getting QR code:', error);
      return null;
    }
  }

  // Format phone number for WhatsApp
  formatPhoneNumber(phoneNumber: string): string {
    // Remove all non-numeric characters
    let formatted = phoneNumber.replace(/\D/g, '');
    
    // Add country code if not present
    if (!formatted.startsWith('1') && formatted.length === 10) {
      formatted = '1' + formatted;
    }
    
    // Add @c.us suffix for WhatsApp
    return formatted + '@c.us';
  }

  // Validate phone number format
  isValidPhoneNumber(phoneNumber: string): boolean {
    const cleaned = phoneNumber.replace(/\D/g, '');
    return cleaned.length >= 10 && cleaned.length <= 15;
  }
}

export const evolutionApiService = new EvolutionApiService();
