import { useQuery } from "@tanstack/react-query";
import { Progress } from "@/components/ui/progress";
import { useWebSocket } from "@/hooks/useWebSocket";
import { useQueryClient } from "@tanstack/react-query";
import { useEffect } from "react";

interface CampaignProgressProps {
  campaignId: string;
}

interface CampaignStats {
  total: number;
  sent: number;
  delivered: number;
  failed: number;
  pending: number;
}

export default function CampaignProgress({ campaignId }: CampaignProgressProps) {
  const queryClient = useQueryClient();

  const { data: stats, isLoading } = useQuery<CampaignStats>({
    queryKey: ["/api/campaigns", campaignId, "stats"],
    refetchInterval: 5000, // Refresh every 5 seconds
  });

  const { subscribeToCampaign } = useWebSocket({
    onMessage: (message) => {
      if (message.type === 'campaign_progress' && message.data.campaignId === campaignId) {
        queryClient.invalidateQueries({ queryKey: ["/api/campaigns", campaignId, "stats"] });
      }
    },
  });

  useEffect(() => {
    subscribeToCampaign(campaignId);
  }, [campaignId, subscribeToCampaign]);

  if (isLoading || !stats) {
    return (
      <div className="space-y-2">
        <div className="flex justify-between text-sm">
          <span className="text-muted-foreground">Progress</span>
          <span className="font-medium text-card-foreground">Loading...</span>
        </div>
        <Progress value={0} className="h-2" />
      </div>
    );
  }

  const progress = stats.total > 0 ? Math.round((stats.sent / stats.total) * 100) : 0;

  return (
    <div className="space-y-3">
      <div className="flex justify-between text-sm">
        <span className="text-muted-foreground">Progress</span>
        <span className="font-medium text-card-foreground">
          {progress}% ({stats.sent}/{stats.total})
        </span>
      </div>
      <Progress value={progress} className="h-2" />
      
      <div className="grid grid-cols-4 gap-4 text-center text-xs">
        <div>
          <div className="font-medium text-blue-600 dark:text-blue-400">{stats.sent}</div>
          <div className="text-muted-foreground">Sent</div>
        </div>
        <div>
          <div className="font-medium text-green-600 dark:text-green-400">{stats.delivered}</div>
          <div className="text-muted-foreground">Delivered</div>
        </div>
        <div>
          <div className="font-medium text-red-600 dark:text-red-400">{stats.failed}</div>
          <div className="text-muted-foreground">Failed</div>
        </div>
        <div>
          <div className="font-medium text-orange-600 dark:text-orange-400">{stats.pending}</div>
          <div className="text-muted-foreground">Pending</div>
        </div>
      </div>
    </div>
  );
}
