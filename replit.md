# WhatsApp Bulk Messenger Platform

## Overview

A full-stack WhatsApp bulk messaging platform that enables users to manage and execute large-scale messaging campaigns through the Evolution API. The application provides contact management, message templating, campaign scheduling, and real-time monitoring capabilities.

## User Preferences

Preferred communication style: Simple, everyday language.

## System Architecture

### Frontend Architecture
- **Framework**: React with TypeScript, built using Vite
- **UI Components**: Radix UI primitives with shadcn/ui component library
- **Styling**: TailwindCSS with CSS variables for theming and dark mode support
- **State Management**: TanStack Query for server state management and caching
- **Routing**: Wouter for lightweight client-side routing
- **Real-time Updates**: WebSocket integration for live campaign monitoring and message status updates

### Backend Architecture
- **Runtime**: Node.js with TypeScript and ES modules
- **Framework**: Express.js with middleware for JSON parsing, logging, and error handling
- **API Design**: RESTful endpoints with real-time WebSocket support
- **File Uploads**: Multer for handling Excel file uploads with in-memory storage
- **Session Management**: Express-session with PostgreSQL store for persistent sessions
- **Task Scheduling**: Node-cron for automated campaign execution and scheduling

### Database Architecture
- **Primary Database**: PostgreSQL with Drizzle ORM
- **Connection**: Neon serverless PostgreSQL with connection pooling
- **Schema**: Relational design with proper foreign key relationships
- **Key Entities**: Users, Evolution API instances, contacts, contact groups, message templates, campaigns, and campaign messages
- **Session Storage**: PostgreSQL-backed session store for authentication persistence

### Authentication System
- **Provider**: Replit OIDC authentication
- **Strategy**: Passport.js with OpenID Connect strategy
- **Session Management**: Secure HTTP-only cookies with PostgreSQL session storage
- **Authorization**: Route-level middleware for protected endpoints

### External API Integration
- **Evolution API**: WhatsApp messaging service integration
- **Connection Management**: Multiple Evolution API instances per user with health monitoring
- **Message Delivery**: Asynchronous message sending with status tracking and retry logic
- **Rate Limiting**: Configurable delays between messages to prevent API throttling

### Campaign Management System
- **Execution Engine**: Asynchronous campaign processing with real-time progress tracking
- **Scheduling**: Cron-based scheduler for automated campaign execution
- **Message Templating**: Variable substitution system for personalized messages
- **Status Tracking**: Comprehensive message status monitoring (pending, sent, delivered, failed)

### File Processing
- **Excel Import**: XLSX library for parsing contact spreadsheets
- **Template Generation**: Automated Excel template creation for contact imports
- **Data Validation**: Schema validation for imported contact data

### Real-time Communication
- **WebSocket Server**: Integrated WebSocket server for live updates
- **Event Broadcasting**: Campaign progress, message status updates, and system notifications
- **Client Reconnection**: Automatic reconnection logic with configurable retry attempts

## External Dependencies

### Core Technologies
- **Database**: Neon PostgreSQL serverless database
- **Authentication**: Replit OIDC service
- **Messaging API**: Evolution API for WhatsApp integration

### Development Tools
- **Build System**: Vite for frontend bundling and development server
- **Type System**: TypeScript for both frontend and backend
- **Database Migrations**: Drizzle Kit for schema management
- **Development Environment**: Replit-specific plugins for enhanced development experience

### Runtime Dependencies
- **UI Library**: Radix UI for accessible component primitives
- **Form Management**: React Hook Form with Zod validation
- **Date Handling**: date-fns for date manipulation and formatting
- **Excel Processing**: XLSX for spreadsheet operations
- **WebSocket**: ws library for real-time communication
- **Task Scheduling**: node-cron for automated job execution