---
description: Repository Information Overview
alwaysApply: true
---

# Bulk Message Master Information

## Summary
Bulk Message Master is a WhatsApp bulk messaging application that allows users to create and manage messaging campaigns. It integrates with Evolution API for WhatsApp messaging, supports contact management, message templating, and campaign scheduling.

## Structure
- **client/**: React frontend application with TypeScript
- **server/**: Express.js backend API with TypeScript
- **shared/**: Common code and database schema shared between client and server
- **server/services/**: Backend services for campaigns, Excel handling, and WhatsApp integration

## Language & Runtime
**Language**: TypeScript/JavaScript
**Version**: ESM modules (type: "module")
**Build System**: Vite for frontend, esbuild for backend
**Package Manager**: npm

## Dependencies
**Main Dependencies**:
- **Frontend**: React 18, React Query, Tailwind CSS, Radix UI components
- **Backend**: Express.js, Drizzle ORM, Neon PostgreSQL
- **Database**: PostgreSQL with Drizzle ORM
- **API Integration**: Evolution API for WhatsApp messaging
- **File Processing**: xlsx for Excel file handling
- **Scheduling**: node-cron for campaign scheduling

**Development Dependencies**:
- TypeScript 5.6.3
- Vite 5.4.19
- esbuild for server bundling
- Tailwind CSS tooling

## Build & Installation
```bash
# Install dependencies
npm install

# Development mode
npm run dev

# Build for production
npm run build

# Start production server
npm run start

# Database schema updates
npm run db:push
```

## Database
**Type**: PostgreSQL
**ORM**: Drizzle ORM
**Schema**: Defined in shared/schema.ts
**Connection**: Neon Serverless PostgreSQL client
**Models**: Users, Evolution Instances, Contact Groups, Contacts, Message Templates, Campaigns, Campaign Messages

## Main Files & Resources
**Backend Entry Point**: server/index.ts
**Frontend Entry Point**: client/src/main.tsx
**API Routes**: server/routes.ts
**Database Connection**: server/db.ts
**Database Schema**: shared/schema.ts
**Services**:
- campaignService.ts: Campaign management
- evolutionApi.ts: WhatsApp API integration
- excelService.ts: Excel file processing
- schedulerService.ts: Campaign scheduling

## Features
- **Authentication**: Replit Auth integration
- **Contact Management**: Import/export contacts via Excel
- **Message Templates**: Create and manage message templates with variables
- **Campaign Management**: Create, schedule, start, pause, and stop campaigns
- **Real-time Updates**: WebSocket integration for campaign status updates
- **Dashboard**: Campaign statistics and recent message tracking
- **WhatsApp Integration**: Connection to Evolution API for WhatsApp messaging