import { useQuery } from "@tanstack/react-query";
import { Card, CardContent } from "@/components/ui/card";

interface DashboardStats {
  totalCampaigns: number;
  messagesSent: number;
  successRate: number;
  activeCampaigns: number;
}

export default function StatsCards() {
  const { data: stats, isLoading } = useQuery<DashboardStats>({
    queryKey: ["/api/dashboard/stats"],
    retry: false,
  });

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="animate-pulse">
            <div className="h-32 bg-muted rounded-lg"></div>
          </div>
        ))}
      </div>
    );
  }

  const statsData = [
    {
      title: "Total Campaigns",
      value: stats?.totalCampaigns || 0,
      icon: "fas fa-bullhorn",
      color: "blue",
      change: "+3",
      changeLabel: "this month",
    },
    {
      title: "Messages Sent",
      value: stats?.messagesSent || 0,
      icon: "fas fa-paper-plane",
      color: "green",
      change: "+12%",
      changeLabel: "vs last month",
    },
    {
      title: "Success Rate",
      value: `${stats?.successRate || 0}%`,
      icon: "fas fa-check-circle",
      color: "emerald",
      change: "+2.1%",
      changeLabel: "improvement",
    },
    {
      title: "Active Campaigns",
      value: stats?.activeCampaigns || 0,
      icon: "fas fa-clock",
      color: "orange",
      change: "2 scheduled",
      changeLabel: "for today",
    },
  ];

  const getColorClasses = (color: string) => {
    switch (color) {
      case "blue": return "bg-blue-100 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400";
      case "green": return "bg-green-100 text-green-600 dark:bg-green-900/20 dark:text-green-400";
      case "emerald": return "bg-emerald-100 text-emerald-600 dark:bg-emerald-900/20 dark:text-emerald-400";
      case "orange": return "bg-orange-100 text-orange-600 dark:bg-orange-900/20 dark:text-orange-400";
      default: return "bg-gray-100 text-gray-600 dark:bg-gray-900/20 dark:text-gray-400";
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      {statsData.map((stat, index) => (
        <Card key={index} className="hover:shadow-md transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">{stat.title}</p>
                <p className="text-3xl font-bold text-card-foreground" data-testid={`stat-${stat.title.toLowerCase().replace(/\s+/g, '-')}`}>
                  {stat.value}
                </p>
              </div>
              <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${getColorClasses(stat.color)}`}>
                <i className={`${stat.icon} text-xl`}></i>
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm">
              <span className="text-green-600 font-medium dark:text-green-400">{stat.change}</span>
              <span className="text-muted-foreground ml-1">{stat.changeLabel}</span>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
