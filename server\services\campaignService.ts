import { storage } from '../storage';
import { evolutionApiService } from './evolutionApi';
import { 
  type Campaign, 
  type CampaignMessage, 
  type Contact, 
  type MessageTemplate,
  type EvolutionInstance,
  type InsertCampaign,
  type InsertCampaignMessage 
} from '@shared/schema';
import { WebSocketServer, WebSocket } from 'ws';

interface CampaignProgress {
  campaignId: string;
  total: number;
  sent: number;
  delivered: number;
  failed: number;
  pending: number;
  progress: number;
  estimatedFinishTime?: Date;
}

class CampaignService {
  private activeProcesses: Map<string, NodeJS.Timeout> = new Map();
  private wsServer?: WebSocketServer;

  setWebSocketServer(wss: WebSocketServer) {
    this.wsServer = wss;
  }

  private broadcastProgress(progress: CampaignProgress) {
    if (!this.wsServer) return;

    this.wsServer.clients.forEach((client: WebSocket) => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(JSON.stringify({
          type: 'campaign_progress',
          data: progress,
        }));
      }
    });
  }

  private broadcastMessageUpdate(message: CampaignMessage) {
    if (!this.wsServer) return;

    this.wsServer.clients.forEach((client: WebSocket) => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(JSON.stringify({
          type: 'message_update',
          data: message,
        }));
      }
    });
  }

  async createCampaign(campaignData: InsertCampaign, contactIds: string[]): Promise<Campaign> {
    // Create the campaign
    const campaign = await storage.createCampaign(campaignData);

    // Get contacts and create campaign messages
    const contacts = await Promise.all(
      contactIds.map(id => storage.getContact(id))
    );

    const validContacts = contacts.filter(Boolean) as Contact[];
    
    if (validContacts.length === 0) {
      throw new Error('No valid contacts selected');
    }

    // Get the message template
    const template = await storage.getMessageTemplate(campaignData.templateId!);
    if (!template) {
      throw new Error('Message template not found');
    }

    // Create campaign messages
    const campaignMessages: InsertCampaignMessage[] = validContacts.map(contact => ({
      campaignId: campaign.id,
      contactId: contact.id,
      phoneNumber: contact.phoneNumber,
      messageContent: this.replaceVariables(template.content, contact, template.variables as string[] || []),
      status: 'pending' as const,
    }));

    await storage.bulkCreateCampaignMessages(campaignMessages);

    // Update campaign with total contacts
    await storage.updateCampaign(campaign.id, {
      totalContacts: validContacts.length,
    });

    // Start campaign if not scheduled
    if (campaignData.status === 'running') {
      await this.startCampaign(campaign.id);
    }

    return campaign;
  }

  private replaceVariables(content: string, contact: Contact, variables: string[]): string {
    let message = content;
    
    // Replace standard variables
    const replacements: Record<string, string> = {
      firstName: contact.firstName || '',
      lastName: contact.lastName || '',
      email: contact.email || '',
      company: contact.company || '',
      phoneNumber: contact.phoneNumber || '',
    };

    // Add custom fields
    if (contact.customFields && typeof contact.customFields === 'object') {
      Object.assign(replacements, contact.customFields);
    }

    // Replace variables in message
    variables.forEach(variable => {
      const value = replacements[variable] || '';
      message = message.replace(new RegExp(`{${variable}}`, 'g'), value);
    });

    return message;
  }

  async startCampaign(campaignId: string): Promise<void> {
    const campaign = await storage.getCampaign(campaignId);
    if (!campaign) {
      throw new Error('Campaign not found');
    }

    if (campaign.status !== 'draft' && campaign.status !== 'paused' && campaign.status !== 'scheduled') {
      throw new Error('Campaign cannot be started in current state');
    }

    // Update campaign status
    await storage.updateCampaign(campaignId, {
      status: 'running',
      startedAt: new Date(),
    });

    // Start processing messages
    this.processMessages(campaignId);
  }

  async pauseCampaign(campaignId: string): Promise<void> {
    const campaign = await storage.getCampaign(campaignId);
    if (!campaign) {
      throw new Error('Campaign not found');
    }

    if (campaign.status !== 'running') {
      throw new Error('Campaign is not running');
    }

    // Clear the active process
    const timeout = this.activeProcesses.get(campaignId);
    if (timeout) {
      clearTimeout(timeout);
      this.activeProcesses.delete(campaignId);
    }

    // Update campaign status
    await storage.updateCampaign(campaignId, {
      status: 'paused',
      pausedAt: new Date(),
    });
  }

  async resumeCampaign(campaignId: string): Promise<void> {
    const campaign = await storage.getCampaign(campaignId);
    if (!campaign) {
      throw new Error('Campaign not found');
    }

    if (campaign.status !== 'paused') {
      throw new Error('Campaign is not paused');
    }

    // Update campaign status
    await storage.updateCampaign(campaignId, {
      status: 'running',
      pausedAt: null,
    });

    // Resume processing messages
    this.processMessages(campaignId);
  }

  async stopCampaign(campaignId: string): Promise<void> {
    const campaign = await storage.getCampaign(campaignId);
    if (!campaign) {
      throw new Error('Campaign not found');
    }

    // Clear the active process
    const timeout = this.activeProcesses.get(campaignId);
    if (timeout) {
      clearTimeout(timeout);
      this.activeProcesses.delete(campaignId);
    }

    // Update campaign status
    await storage.updateCampaign(campaignId, {
      status: 'cancelled',
      completedAt: new Date(),
    });
  }

  private async processMessages(campaignId: string): Promise<void> {
    const campaign = await storage.getCampaign(campaignId);
    if (!campaign || campaign.status !== 'running') {
      return;
    }

    // Get Evolution API instance
    const instance = await storage.getEvolutionInstance(campaign.evolutionInstanceId!);
    if (!instance) {
      console.error('Evolution API instance not found for campaign:', campaignId);
      return;
    }

    // Get pending messages
    const messages = await storage.getCampaignMessages(campaignId);
    const pendingMessages = messages.filter(m => m.status === 'pending');

    if (pendingMessages.length === 0) {
      // Campaign completed
      await storage.updateCampaign(campaignId, {
        status: 'completed',
        completedAt: new Date(),
      });
      
      // Update final progress
      await this.updateCampaignProgress(campaignId);
      return;
    }

    // Process next message
    const nextMessage = pendingMessages[0];
    await this.sendMessage(nextMessage, instance, campaign);

    // Schedule next message with random delay
    const minDelay = (campaign.minDelay || 5) * 1000;
    const maxDelay = (campaign.maxDelay || 15) * 1000;
    const delay = Math.floor(Math.random() * (maxDelay - minDelay + 1)) + minDelay;

    const timeout = setTimeout(() => {
      this.processMessages(campaignId);
    }, delay);

    this.activeProcesses.set(campaignId, timeout);
  }

  private async sendMessage(message: CampaignMessage, instance: EvolutionInstance, campaign: Campaign): Promise<void> {
    try {
      // Update message status to sending
      await storage.updateCampaignMessage(message.id, {
        status: 'sending',
      });

      const updatedMessage = await storage.getCampaignMessage(message.id);
      if (updatedMessage) {
        this.broadcastMessageUpdate(updatedMessage);
      }

      // Format phone number
      const formattedNumber = evolutionApiService.formatPhoneNumber(message.phoneNumber);

      // Send message via Evolution API
      const result = await evolutionApiService.sendMessage(
        {
          baseUrl: instance.evolutionUrl,
          apiKey: instance.apiKey,
          instanceKey: instance.instanceKey,
        },
        {
          number: formattedNumber,
          text: message.messageContent,
        }
      );

      // Update message status based on result
      const updateData: Partial<InsertCampaignMessage> = {
        sentAt: new Date(),
      };

      if (result.success) {
        updateData.status = 'sent';
        updateData.evolutionMessageId = result.messageId;
        
        // Update campaign sent count
        await storage.updateCampaign(campaign.id, {
          sentCount: (campaign.sentCount || 0) + 1,
        });
      } else {
        updateData.status = 'failed';
        updateData.errorMessage = result.error;
        
        // Update campaign failed count
        await storage.updateCampaign(campaign.id, {
          failedCount: (campaign.failedCount || 0) + 1,
        });
      }

      await storage.updateCampaignMessage(message.id, updateData);

      const finalMessage = await storage.getCampaignMessage(message.id);
      if (finalMessage) {
        this.broadcastMessageUpdate(finalMessage);
      }

      // Update campaign progress
      await this.updateCampaignProgress(campaign.id);

    } catch (error) {
      console.error('Error sending message:', error);
      
      await storage.updateCampaignMessage(message.id, {
        status: 'failed',
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
      });

      await storage.updateCampaign(campaign.id, {
        failedCount: (campaign.failedCount || 0) + 1,
      });

      const failedMessage = await storage.getCampaignMessage(message.id);
      if (failedMessage) {
        this.broadcastMessageUpdate(failedMessage);
      }

      await this.updateCampaignProgress(campaign.id);
    }
  }

  private async updateCampaignProgress(campaignId: string): Promise<void> {
    const stats = await storage.getCampaignStats(campaignId);
    const campaign = await storage.getCampaign(campaignId);
    
    if (!campaign) return;

    const progress = stats.total > 0 ? Math.round((stats.sent / stats.total) * 100) : 0;
    
    // Calculate estimated finish time
    let estimatedFinishTime: Date | undefined;
    if (stats.pending > 0 && campaign.status === 'running') {
      const avgDelay = ((campaign.minDelay || 5) + (campaign.maxDelay || 15)) / 2;
      const remainingTime = stats.pending * avgDelay * 1000;
      estimatedFinishTime = new Date(Date.now() + remainingTime);
    }

    const progressData: CampaignProgress = {
      campaignId,
      total: stats.total,
      sent: stats.sent,
      delivered: stats.delivered,
      failed: stats.failed,
      pending: stats.pending,
      progress,
      estimatedFinishTime,
    };

    this.broadcastProgress(progressData);
  }

  async getCampaignProgress(campaignId: string): Promise<CampaignProgress> {
    const stats = await storage.getCampaignStats(campaignId);
    const campaign = await storage.getCampaign(campaignId);
    
    if (!campaign) {
      throw new Error('Campaign not found');
    }

    const progress = stats.total > 0 ? Math.round((stats.sent / stats.total) * 100) : 0;
    
    let estimatedFinishTime: Date | undefined;
    if (stats.pending > 0 && campaign.status === 'running') {
      const avgDelay = ((campaign.minDelay || 5) + (campaign.maxDelay || 15)) / 2;
      const remainingTime = stats.pending * avgDelay * 1000;
      estimatedFinishTime = new Date(Date.now() + remainingTime);
    }

    return {
      campaignId,
      total: stats.total,
      sent: stats.sent,
      delivered: stats.delivered,
      failed: stats.failed,
      pending: stats.pending,
      progress,
      estimatedFinishTime,
    };
  }
}

export const campaignService = new CampaignService();
