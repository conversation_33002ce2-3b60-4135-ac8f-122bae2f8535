import { drizzle } from 'drizzle-orm/sqlite-core';
import { sqliteTable, integer, text, primaryKey } from 'drizzle-orm/sqlite-core';
import Database from 'better-sqlite3';
import * as schema from "@shared/schema";
import { migrate } from 'drizzle-orm/better-sqlite3/migrator';
import path from 'path';

const isDevelopment = process.env.NODE_ENV === 'development';

let db: ReturnType<typeof drizzle>;

if (isDevelopment) {
  // Use SQLite for development
  const sqlite = new Database(':memory:');
  sqlite.pragma('journal_mode = WAL');
  db = drizzle(sqlite, { schema });
  
  // Auto-migrate in development
  try {
    migrate({ drizzleClient: sqlite }).then(() => {
      console.log('Development database migrated');
    });
  } catch (error) {
    console.error('Error migrating development database:', error);
  }
} else {
  // Use Neon for production
  const { Pool, neonConfig } = require('@neondatabase/serverless');
  const ws = require('ws');
  
  neonConfig.webSocketConstructor = ws;
  
  if (!process.env.DATABASE_URL) {
    throw new Error(
      "DATABASE_URL must be set. Did you forget to provision a database?",
    );
  }
  
  const pool = new Pool({ connectionString: process.env.DATABASE_URL });
  db = require('drizzle-orm/neon-serverless').drizzle({ client: pool, schema });
}

export { db };