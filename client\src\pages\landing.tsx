import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";

export default function Landing() {
  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardContent className="pt-6">
          <div className="text-center mb-6">
            <div className="flex items-center justify-center mb-4">
              <i className="fab fa-whatsapp text-6xl text-green-500"></i>
            </div>
            <h2 className="text-2xl font-bold text-card-foreground">WhatsApp Bulk Messenger</h2>
            <p className="text-muted-foreground">Sign in to manage your messaging campaigns</p>
          </div>
          
          <div className="space-y-4">
            <Button 
              onClick={() => window.location.href = '/api/login'}
              className="w-full"
              data-testid="button-login"
            >
              Sign In with Replit
            </Button>
          </div>
          
          <div className="mt-6 pt-4 border-t border-border">
            <div className="text-sm text-muted-foreground">
              <p><strong>Features:</strong></p>
              <ul className="list-disc list-inside space-y-1 mt-2">
                <li>Bulk WhatsApp messaging</li>
                <li>Excel contact import</li>
                <li>Message templates with variables</li>
                <li>Real-time campaign tracking</li>
                <li>Campaign scheduling</li>
                <li>Evolution API integration</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
