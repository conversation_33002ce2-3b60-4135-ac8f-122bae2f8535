@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: hsl(210, 20%, 96%);
  --foreground: hsl(210, 25%, 12%);
  --card: hsl(0, 0%, 100%);
  --card-foreground: hsl(210, 25%, 15%);
  --popover: hsl(0, 0%, 100%);
  --popover-foreground: hsl(210, 25%, 15%);
  --primary: hsl(213, 93%, 48%);
  --primary-foreground: hsl(0, 0%, 100%);
  --secondary: hsl(213, 20%, 95%);
  --secondary-foreground: hsl(213, 25%, 25%);
  --muted: hsl(213, 15%, 92%);
  --muted-foreground: hsl(213, 20%, 40%);
  --accent: hsl(213, 30%, 88%);
  --accent-foreground: hsl(213, 30%, 25%);
  --destructive: hsl(0, 84%, 60%);
  --destructive-foreground: hsl(0, 0%, 100%);
  --border: hsl(213, 20%, 85%);
  --input: hsl(213, 20%, 90%);
  --ring: hsl(213, 93%, 48%);
  --chart-1: hsl(213, 93%, 48%);
  --chart-2: hsl(159, 100%, 36%);
  --chart-3: hsl(42, 93%, 56%);
  --chart-4: hsl(147, 78%, 42%);
  --chart-5: hsl(341, 75%, 51%);
  --sidebar: hsl(0, 0%, 100%);
  --sidebar-foreground: hsl(210, 25%, 15%);
  --sidebar-primary: hsl(213, 93%, 48%);
  --sidebar-primary-foreground: hsl(0, 0%, 100%);
  --sidebar-accent: hsl(213, 30%, 88%);
  --sidebar-accent-foreground: hsl(213, 30%, 25%);
  --sidebar-border: hsl(213, 20%, 85%);
  --sidebar-ring: hsl(213, 93%, 48%);
  --font-sans: "Inter", system-ui, sans-serif;
  --font-serif: Georgia, serif;
  --font-mono: Menlo, monospace;
  --radius: 8px;
  --spacing: 0.25rem;
}

.dark {
  --background: hsl(0, 0%, 0%);
  --foreground: hsl(200, 6.7%, 91.2%);
  --card: hsl(228, 9.8%, 10%);
  --card-foreground: hsl(0, 0%, 85.1%);
  --popover: hsl(0, 0%, 0%);
  --popover-foreground: hsl(200, 6.7%, 91.2%);
  --primary: hsl(213, 93%, 48%);
  --primary-foreground: hsl(0, 0%, 100%);
  --secondary: hsl(195, 15.4%, 94.9%);
  --secondary-foreground: hsl(210, 25%, 7.8%);
  --muted: hsl(0, 0%, 9.4%);
  --muted-foreground: hsl(210, 3.4%, 46.3%);
  --accent: hsl(205, 70%, 7.8%);
  --accent-foreground: hsl(213, 93%, 48%);
  --destructive: hsl(0, 84%, 60%);
  --destructive-foreground: hsl(0, 0%, 100%);
  --border: hsl(210, 5.3%, 14.9%);
  --input: hsl(208, 27.7%, 18.4%);
  --ring: hsl(213, 93%, 48%);
  --chart-1: hsl(213, 93%, 48%);
  --chart-2: hsl(159, 100%, 36%);
  --chart-3: hsl(42, 93%, 56%);
  --chart-4: hsl(147, 78%, 42%);
  --chart-5: hsl(341, 75%, 51%);
  --sidebar: hsl(228, 9.8%, 10%);
  --sidebar-foreground: hsl(0, 0%, 85.1%);
  --sidebar-primary: hsl(213, 93%, 48%);
  --sidebar-primary-foreground: hsl(0, 0%, 100%);
  --sidebar-accent: hsl(205, 70%, 7.8%);
  --sidebar-accent-foreground: hsl(213, 93%, 48%);
  --sidebar-border: hsl(205, 15.8%, 26.1%);
  --sidebar-ring: hsl(213, 93%, 48%);
  --font-sans: "Inter", system-ui, sans-serif;
  --font-serif: Georgia, serif;
  --font-mono: Menlo, monospace;
  --radius: 8px;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
  }
}

.progress-bar {
  transition: width 0.3s ease-in-out;
}

.pulse-green {
  animation: pulse-green 2s infinite;
}

@keyframes pulse-green {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}
