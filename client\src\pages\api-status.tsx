import { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { apiRequest } from "@/lib/queryClient";
import { isUnauthorizedError } from "@/lib/authUtils";
import type { EvolutionInstance } from "@shared/schema";

export default function ApiStatus() {
  const { toast } = useToast();
  const { isAuthenticated, isLoading } = useAuth();
  const queryClient = useQueryClient();
  const [showAddInstance, setShowAddInstance] = useState(false);
  const [newInstance, setNewInstance] = useState({
    name: "",
    instanceKey: "",
    evolutionUrl: "",
    apiKey: "",
  });

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      toast({
        title: "Unauthorized",
        description: "You are logged out. Logging in again...",
        variant: "destructive",
      });
      setTimeout(() => {
        window.location.href = "/api/login";
      }, 500);
      return;
    }
  }, [isAuthenticated, isLoading, toast]);

  const { data: instances, isLoading: instancesLoading } = useQuery<EvolutionInstance[]>({
    queryKey: ["/api/evolution-instances"],
    enabled: isAuthenticated,
  });

  const addInstanceMutation = useMutation({
    mutationFn: async (data: typeof newInstance) => {
      return await apiRequest("POST", "/api/evolution-instances", data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/evolution-instances"] });
      toast({ title: "Success", description: "Evolution API instance added successfully" });
      setShowAddInstance(false);
      setNewInstance({ name: "", instanceKey: "", evolutionUrl: "", apiKey: "" });
    },
    onError: (error) => {
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const deleteInstanceMutation = useMutation({
    mutationFn: async (instanceId: string) => {
      return await apiRequest("DELETE", `/api/evolution-instances/${instanceId}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/evolution-instances"] });
      toast({ title: "Success", description: "Evolution API instance deleted successfully" });
    },
    onError: (error) => {
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case "connected": return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300";
      case "disconnected": return "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300";
      case "connecting": return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300";
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "connected": return "fas fa-check-circle text-green-500";
      case "disconnected": return "fas fa-times-circle text-red-500";
      case "connecting": return "fas fa-spinner fa-spin text-yellow-500";
      default: return "fas fa-question-circle text-gray-500";
    }
  };

  const handleAddInstance = (e: React.FormEvent) => {
    e.preventDefault();
    if (!newInstance.name || !newInstance.instanceKey || !newInstance.evolutionUrl || !newInstance.apiKey) {
      toast({
        title: "Validation Error",
        description: "All fields are required",
        variant: "destructive",
      });
      return;
    }
    addInstanceMutation.mutate(newInstance);
  };

  if (isLoading || instancesLoading) {
    return (
      <div className="flex-1 overflow-y-auto bg-background p-6">
        <div className="animate-pulse space-y-6">
          <div className="flex justify-between items-center">
            <div className="h-8 bg-muted rounded w-48"></div>
            <div className="h-10 bg-muted rounded w-32"></div>
          </div>
          <div className="grid gap-6">
            {[...Array(2)].map((_, i) => (
              <div key={i} className="h-40 bg-muted rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="flex-1 overflow-y-auto bg-background p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-bold text-card-foreground">API Status</h2>
          <p className="text-muted-foreground">Monitor and manage Evolution API connections</p>
        </div>
        <Button onClick={() => setShowAddInstance(true)} data-testid="button-add-instance">
          <i className="fas fa-plus mr-2"></i>
          Add Instance
        </Button>
      </div>

      <div className="grid gap-6">
        {instances?.map((instance) => (
          <Card key={instance.id} className="hover:shadow-md transition-shadow">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center space-x-3">
                  <i className={getStatusIcon(instance.status)}></i>
                  <span>{instance.name}</span>
                </CardTitle>
                <div className="flex items-center space-x-2">
                  <Badge className={getStatusColor(instance.status)}>
                    {instance.status}
                  </Badge>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => deleteInstanceMutation.mutate(instance.id)}
                    disabled={deleteInstanceMutation.isPending}
                    data-testid={`button-delete-instance-${instance.id}`}
                  >
                    <i className="fas fa-trash text-red-500"></i>
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Instance Key</label>
                  <p className="text-card-foreground font-mono text-sm">{instance.instanceKey}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Evolution URL</label>
                  <p className="text-card-foreground text-sm break-all">{instance.evolutionUrl}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">API Key</label>
                  <p className="text-card-foreground font-mono text-sm">
                    {instance.apiKey.substring(0, 8)}...
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Last Updated</label>
                  <p className="text-card-foreground text-sm">
                    {new Date(instance.updatedAt).toLocaleString()}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}

        {instances?.length === 0 && (
          <Card>
            <CardContent className="text-center py-12">
              <i className="fas fa-plug text-4xl text-muted-foreground mb-4"></i>
              <h3 className="text-lg font-semibold text-card-foreground mb-2">No API Instances</h3>
              <p className="text-muted-foreground mb-4">
                Add your first Evolution API instance to start sending WhatsApp messages.
              </p>
              <Button onClick={() => setShowAddInstance(true)}>
                <i className="fas fa-plus mr-2"></i>
                Add Instance
              </Button>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Add Instance Modal */}
      <Dialog open={showAddInstance} onOpenChange={setShowAddInstance}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Evolution API Instance</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleAddInstance} className="space-y-4">
            <div>
              <Label htmlFor="name">Instance Name</Label>
              <Input
                id="name"
                value={newInstance.name}
                onChange={(e) => setNewInstance({ ...newInstance, name: e.target.value })}
                placeholder="My WhatsApp Instance"
                data-testid="input-instance-name"
              />
            </div>
            <div>
              <Label htmlFor="instanceKey">Instance Key</Label>
              <Input
                id="instanceKey"
                value={newInstance.instanceKey}
                onChange={(e) => setNewInstance({ ...newInstance, instanceKey: e.target.value })}
                placeholder="my-instance-key"
                data-testid="input-instance-key"
              />
            </div>
            <div>
              <Label htmlFor="evolutionUrl">Evolution API URL</Label>
              <Input
                id="evolutionUrl"
                value={newInstance.evolutionUrl}
                onChange={(e) => setNewInstance({ ...newInstance, evolutionUrl: e.target.value })}
                placeholder="https://your-evolution-api.com"
                data-testid="input-evolution-url"
              />
            </div>
            <div>
              <Label htmlFor="apiKey">API Key</Label>
              <Input
                id="apiKey"
                type="password"
                value={newInstance.apiKey}
                onChange={(e) => setNewInstance({ ...newInstance, apiKey: e.target.value })}
                placeholder="Your API Key"
                data-testid="input-api-key"
              />
            </div>
            <div className="flex justify-between pt-4">
              <Button type="button" variant="outline" onClick={() => setShowAddInstance(false)}>
                Cancel
              </Button>
              <Button 
                type="submit" 
                disabled={addInstanceMutation.isPending}
                data-testid="button-save-instance"
              >
                {addInstanceMutation.isPending ? "Testing Connection..." : "Add Instance"}
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
