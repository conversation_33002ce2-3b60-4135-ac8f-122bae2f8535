import type { Express } from "express";
import { createServer, type Server } from "http";
import { WebSocketServer, WebSocket } from "ws";
import { storage } from "./storage";
import { setupAuth, isAuthenticated } from "./replitAuth";
import { evolutionApiService } from "./services/evolutionApi";
import { campaignService } from "./services/campaignService";
import { excelService } from "./services/excelService";
import { schedulerService } from "./services/schedulerService";
import { 
  insertEvolutionInstanceSchema,
  insertContactGroupSchema,
  insertContactSchema,
  insertMessageTemplateSchema,
  insertCampaignSchema,
} from "@shared/schema";
import multer from 'multer';
import { z } from 'zod';

const upload = multer({ storage: multer.memoryStorage() });

export async function registerRoutes(app: Express): Promise<Server> {
  // Auth middleware
  await setupAuth(app);

  // Auth routes
  app.get('/api/auth/user', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const user = await storage.getUser(userId);
      res.json(user);
    } catch (error) {
      console.error("Error fetching user:", error);
      res.status(500).json({ message: "Failed to fetch user" });
    }
  });

  // Evolution API Instance routes
  app.post('/api/evolution-instances', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const data = insertEvolutionInstanceSchema.parse({ ...req.body, userId });
      
      // Test connection before saving
      const isConnected = await evolutionApiService.testConnection(data.evolutionUrl, data.apiKey, data.instanceKey);
      if (!isConnected) {
        return res.status(400).json({ message: "Failed to connect to Evolution API" });
      }

      const instance = await storage.createEvolutionInstance(data);
      res.json(instance);
    } catch (error) {
      console.error("Error creating evolution instance:", error);
      res.status(500).json({ message: "Failed to create evolution instance" });
    }
  });

  app.get('/api/evolution-instances', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const instances = await storage.getEvolutionInstances(userId);
      res.json(instances);
    } catch (error) {
      console.error("Error fetching evolution instances:", error);
      res.status(500).json({ message: "Failed to fetch evolution instances" });
    }
  });

  app.delete('/api/evolution-instances/:id', isAuthenticated, async (req: any, res) => {
    try {
      const { id } = req.params;
      await storage.deleteEvolutionInstance(id);
      res.json({ success: true });
    } catch (error) {
      console.error("Error deleting evolution instance:", error);
      res.status(500).json({ message: "Failed to delete evolution instance" });
    }
  });

  // Contact Group routes
  app.post('/api/contact-groups', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const data = insertContactGroupSchema.parse({ ...req.body, userId });
      const group = await storage.createContactGroup(data);
      res.json(group);
    } catch (error) {
      console.error("Error creating contact group:", error);
      res.status(500).json({ message: "Failed to create contact group" });
    }
  });

  app.get('/api/contact-groups', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const groups = await storage.getContactGroups(userId);
      res.json(groups);
    } catch (error) {
      console.error("Error fetching contact groups:", error);
      res.status(500).json({ message: "Failed to fetch contact groups" });
    }
  });

  // Contact routes
  app.post('/api/contacts', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const data = insertContactSchema.parse({ ...req.body, userId });
      const contact = await storage.createContact(data);
      res.json(contact);
    } catch (error) {
      console.error("Error creating contact:", error);
      res.status(500).json({ message: "Failed to create contact" });
    }
  });

  app.get('/api/contacts', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const { groupId } = req.query;
      const contacts = await storage.getContacts(userId, groupId as string);
      res.json(contacts);
    } catch (error) {
      console.error("Error fetching contacts:", error);
      res.status(500).json({ message: "Failed to fetch contacts" });
    }
  });

  app.post('/api/contacts/import', isAuthenticated, upload.single('file'), async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      if (!req.file) {
        return res.status(400).json({ message: "No file uploaded" });
      }

      const contacts = await excelService.parseContactsFromExcel(req.file.buffer, userId);
      const createdContacts = await storage.bulkCreateContacts(contacts);
      res.json({ imported: createdContacts.length, contacts: createdContacts });
    } catch (error) {
      console.error("Error importing contacts:", error);
      res.status(500).json({ message: "Failed to import contacts" });
    }
  });

  app.get('/api/contacts/template', (req, res) => {
    try {
      const templateBuffer = excelService.generateContactTemplate();
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', 'attachment; filename=contacts_template.xlsx');
      res.send(templateBuffer);
    } catch (error) {
      console.error("Error generating template:", error);
      res.status(500).json({ message: "Failed to generate template" });
    }
  });

  // Message Template routes
  app.post('/api/templates', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const data = insertMessageTemplateSchema.parse({ ...req.body, userId });
      const template = await storage.createMessageTemplate(data);
      res.json(template);
    } catch (error) {
      console.error("Error creating template:", error);
      res.status(500).json({ message: "Failed to create template" });
    }
  });

  app.get('/api/templates', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const templates = await storage.getMessageTemplates(userId);
      res.json(templates);
    } catch (error) {
      console.error("Error fetching templates:", error);
      res.status(500).json({ message: "Failed to fetch templates" });
    }
  });

  // Campaign routes
  app.post('/api/campaigns', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const data = insertCampaignSchema.parse({ ...req.body, userId });
      
      const campaign = await campaignService.createCampaign(data, req.body.contactIds || []);
      res.json(campaign);
    } catch (error) {
      console.error("Error creating campaign:", error);
      res.status(500).json({ message: "Failed to create campaign" });
    }
  });

  app.get('/api/campaigns', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const campaigns = await storage.getCampaigns(userId);
      res.json(campaigns);
    } catch (error) {
      console.error("Error fetching campaigns:", error);
      res.status(500).json({ message: "Failed to fetch campaigns" });
    }
  });

  app.get('/api/campaigns/active', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const campaigns = await storage.getActiveCampaigns(userId);
      res.json(campaigns);
    } catch (error) {
      console.error("Error fetching active campaigns:", error);
      res.status(500).json({ message: "Failed to fetch active campaigns" });
    }
  });

  app.post('/api/campaigns/:id/start', isAuthenticated, async (req: any, res) => {
    try {
      const { id } = req.params;
      await campaignService.startCampaign(id);
      res.json({ success: true });
    } catch (error) {
      console.error("Error starting campaign:", error);
      res.status(500).json({ message: "Failed to start campaign" });
    }
  });

  app.post('/api/campaigns/:id/pause', isAuthenticated, async (req: any, res) => {
    try {
      const { id } = req.params;
      await campaignService.pauseCampaign(id);
      res.json({ success: true });
    } catch (error) {
      console.error("Error pausing campaign:", error);
      res.status(500).json({ message: "Failed to pause campaign" });
    }
  });

  app.post('/api/campaigns/:id/resume', isAuthenticated, async (req: any, res) => {
    try {
      const { id } = req.params;
      await campaignService.resumeCampaign(id);
      res.json({ success: true });
    } catch (error) {
      console.error("Error resuming campaign:", error);
      res.status(500).json({ message: "Failed to resume campaign" });
    }
  });

  app.post('/api/campaigns/:id/stop', isAuthenticated, async (req: any, res) => {
    try {
      const { id } = req.params;
      await campaignService.stopCampaign(id);
      res.json({ success: true });
    } catch (error) {
      console.error("Error stopping campaign:", error);
      res.status(500).json({ message: "Failed to stop campaign" });
    }
  });

  app.get('/api/campaigns/:id/stats', isAuthenticated, async (req: any, res) => {
    try {
      const { id } = req.params;
      const stats = await storage.getCampaignStats(id);
      res.json(stats);
    } catch (error) {
      console.error("Error fetching campaign stats:", error);
      res.status(500).json({ message: "Failed to fetch campaign stats" });
    }
  });

  // Dashboard routes
  app.get('/api/dashboard/stats', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const stats = await storage.getDashboardStats(userId);
      res.json(stats);
    } catch (error) {
      console.error("Error fetching dashboard stats:", error);
      res.status(500).json({ message: "Failed to fetch dashboard stats" });
    }
  });

  app.get('/api/dashboard/recent-messages', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const limit = parseInt(req.query.limit as string) || 20;
      const messages = await storage.getRecentMessages(userId, limit);
      res.json(messages);
    } catch (error) {
      console.error("Error fetching recent messages:", error);
      res.status(500).json({ message: "Failed to fetch recent messages" });
    }
  });

  const httpServer = createServer(app);

  // WebSocket setup for real-time updates
  const wss = new WebSocketServer({ server: httpServer, path: '/ws' });

  wss.on('connection', (ws: WebSocket) => {
    console.log('WebSocket client connected');

    ws.on('message', (message) => {
      try {
        const data = JSON.parse(message.toString());
        if (data.type === 'subscribe' && data.campaignId) {
          // Subscribe to campaign updates
          ws.send(JSON.stringify({ type: 'subscribed', campaignId: data.campaignId }));
        }
      } catch (error) {
        console.error('WebSocket message error:', error);
      }
    });

    ws.on('close', () => {
      console.log('WebSocket client disconnected');
    });
  });

  // Set up campaign service with WebSocket for real-time updates
  campaignService.setWebSocketServer(wss);

  // Start scheduler service
  schedulerService.start();

  return httpServer;
}
