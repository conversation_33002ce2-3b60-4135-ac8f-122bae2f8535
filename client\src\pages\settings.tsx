import { useEffect } from "react";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { isUnauthorizedError } from "@/lib/authUtils";

export default function Settings() {
  const { toast } = useToast();
  const { user, isAuthenticated, isLoading } = useAuth();

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      toast({
        title: "Unauthorized",
        description: "You are logged out. Logging in again...",
        variant: "destructive",
      });
      setTimeout(() => {
        window.location.href = "/api/login";
      }, 500);
      return;
    }
  }, [isAuthenticated, isLoading, toast]);

  if (isLoading) {
    return (
      <div className="flex-1 overflow-y-auto bg-background p-6">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-muted rounded w-48"></div>
          <div className="grid gap-6">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-40 bg-muted rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="flex-1 overflow-y-auto bg-background p-6">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Profile Settings */}
        <Card>
          <CardHeader>
            <CardTitle>Profile Settings</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-4">
              <img 
                src={user?.profileImageUrl || "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=80&h=80"} 
                alt="Profile" 
                className="w-16 h-16 rounded-full object-cover"
              />
              <div className="flex-1">
                <h3 className="font-medium text-card-foreground">
                  {user?.firstName || user?.email || "User"}
                </h3>
                <p className="text-sm text-muted-foreground">{user?.email}</p>
                <p className="text-sm text-muted-foreground">Role: {user?.role || "Agent"}</p>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="firstName">First Name</Label>
                <Input
                  id="firstName"
                  value={user?.firstName || ""}
                  readOnly
                  className="bg-muted"
                  data-testid="input-first-name"
                />
              </div>
              <div>
                <Label htmlFor="lastName">Last Name</Label>
                <Input
                  id="lastName"
                  value={user?.lastName || ""}
                  readOnly
                  className="bg-muted"
                  data-testid="input-last-name"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                value={user?.email || ""}
                readOnly
                className="bg-muted"
                data-testid="input-email"
              />
            </div>

            <div className="pt-4 border-t border-border">
              <p className="text-sm text-muted-foreground">
                Profile information is managed through your Replit account.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Notification Settings */}
        <Card>
          <CardHeader>
            <CardTitle>Notification Settings</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="campaign-notifications">Campaign Notifications</Label>
                <p className="text-sm text-muted-foreground">
                  Get notified when campaigns start, complete, or fail
                </p>
              </div>
              <Switch id="campaign-notifications" defaultChecked data-testid="switch-campaign-notifications" />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="message-notifications">Message Status Updates</Label>
                <p className="text-sm text-muted-foreground">
                  Real-time notifications for message delivery status
                </p>
              </div>
              <Switch id="message-notifications" defaultChecked data-testid="switch-message-notifications" />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="api-notifications">API Status Alerts</Label>
                <p className="text-sm text-muted-foreground">
                  Get alerted when Evolution API connections go down
                </p>
              </div>
              <Switch id="api-notifications" defaultChecked data-testid="switch-api-notifications" />
            </div>
          </CardContent>
        </Card>

        {/* Campaign Settings */}
        <Card>
          <CardHeader>
            <CardTitle>Default Campaign Settings</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="default-min-delay">Default Min Delay (seconds)</Label>
                <Input
                  id="default-min-delay"
                  type="number"
                  defaultValue="5"
                  min="1"
                  data-testid="input-default-min-delay"
                />
              </div>
              <div>
                <Label htmlFor="default-max-delay">Default Max Delay (seconds)</Label>
                <Input
                  id="default-max-delay"
                  type="number"
                  defaultValue="15"
                  min="1"
                  data-testid="input-default-max-delay"
                />
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="auto-retry">Auto Retry Failed Messages</Label>
                <p className="text-sm text-muted-foreground">
                  Automatically retry sending failed messages up to 3 times
                </p>
              </div>
              <Switch id="auto-retry" data-testid="switch-auto-retry" />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="pause-on-error">Pause Campaign on High Error Rate</Label>
                <p className="text-sm text-muted-foreground">
                  Automatically pause campaigns if error rate exceeds 20%
                </p>
              </div>
              <Switch id="pause-on-error" defaultChecked data-testid="switch-pause-on-error" />
            </div>
          </CardContent>
        </Card>

        {/* Data Management */}
        <Card>
          <CardHeader>
            <CardTitle>Data Management</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Button variant="outline" data-testid="button-export-data">
                <i className="fas fa-download mr-2"></i>
                Export All Data
              </Button>
              <Button variant="outline" data-testid="button-clear-completed">
                <i className="fas fa-trash mr-2"></i>
                Clear Completed Campaigns
              </Button>
            </div>

            <div className="pt-4 border-t border-border">
              <div className="flex items-center justify-between">
                <div>
                  <Label>Account Created</Label>
                  <p className="text-sm text-muted-foreground">
                    {user?.createdAt ? new Date(user.createdAt).toLocaleDateString() : "Unknown"}
                  </p>
                </div>
                <Button variant="destructive" data-testid="button-delete-account">
                  <i className="fas fa-exclamation-triangle mr-2"></i>
                  Delete Account
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Save Settings */}
        <div className="flex justify-end">
          <Button data-testid="button-save-settings">
            <i className="fas fa-save mr-2"></i>
            Save Settings
          </Button>
        </div>
      </div>
    </div>
  );
}
