import {
  users,
  evolutionInstances,
  contactGroups,
  contacts,
  messageTemplates,
  campaigns,
  campaignMessages,
  type User,
  type UpsertUser,
  type EvolutionInstance,
  type InsertEvolutionInstance,
  type ContactGroup,
  type InsertContactGroup,
  type Contact,
  type InsertContact,
  type MessageTemplate,
  type InsertMessageTemplate,
  type Campaign,
  type InsertCampaign,
  type CampaignMessage,
  type InsertCampaignMessage,
} from "@shared/schema";
import { db } from "./db";
import { eq, and, desc, asc, sql, count } from "drizzle-orm";

export interface IStorage {
  // User operations (mandatory for Replit Auth)
  getUser(id: string): Promise<User | undefined>;
  upsertUser(user: UpsertUser): Promise<User>;

  // Evolution API operations
  createEvolutionInstance(instance: InsertEvolutionInstance): Promise<EvolutionInstance>;
  getEvolutionInstances(userId: string): Promise<EvolutionInstance[]>;
  getEvolutionInstance(id: string): Promise<EvolutionInstance | undefined>;
  updateEvolutionInstanceStatus(id: string, status: string): Promise<void>;
  deleteEvolutionInstance(id: string): Promise<void>;

  // Contact operations
  createContactGroup(group: InsertContactGroup): Promise<ContactGroup>;
  getContactGroups(userId: string): Promise<ContactGroup[]>;
  getContactGroup(id: string): Promise<ContactGroup | undefined>;
  updateContactGroup(id: string, data: Partial<InsertContactGroup>): Promise<ContactGroup>;
  deleteContactGroup(id: string): Promise<void>;

  createContact(contact: InsertContact): Promise<Contact>;
  getContacts(userId: string, groupId?: string): Promise<Contact[]>;
  getContact(id: string): Promise<Contact | undefined>;
  getContactByPhone(phoneNumber: string, userId: string): Promise<Contact | undefined>;
  updateContact(id: string, data: Partial<InsertContact>): Promise<Contact>;
  deleteContact(id: string): Promise<void>;
  bulkCreateContacts(contacts: InsertContact[]): Promise<Contact[]>;

  // Template operations
  createMessageTemplate(template: InsertMessageTemplate): Promise<MessageTemplate>;
  getMessageTemplates(userId: string): Promise<MessageTemplate[]>;
  getMessageTemplate(id: string): Promise<MessageTemplate | undefined>;
  updateMessageTemplate(id: string, data: Partial<InsertMessageTemplate>): Promise<MessageTemplate>;
  deleteMessageTemplate(id: string): Promise<void>;

  // Campaign operations
  createCampaign(campaign: InsertCampaign): Promise<Campaign>;
  getCampaigns(userId: string): Promise<Campaign[]>;
  getCampaign(id: string): Promise<Campaign | undefined>;
  updateCampaign(id: string, data: Partial<InsertCampaign>): Promise<Campaign>;
  deleteCampaign(id: string): Promise<void>;
  getActiveCampaigns(userId: string): Promise<Campaign[]>;
  getScheduledCampaigns(): Promise<Campaign[]>;

  // Campaign message operations
  createCampaignMessage(message: InsertCampaignMessage): Promise<CampaignMessage>;
  getCampaignMessages(campaignId: string): Promise<CampaignMessage[]>;
  getCampaignMessage(id: string): Promise<CampaignMessage | undefined>;
  updateCampaignMessage(id: string, data: Partial<InsertCampaignMessage>): Promise<CampaignMessage>;
  bulkCreateCampaignMessages(messages: InsertCampaignMessage[]): Promise<CampaignMessage[]>;
  getRecentMessages(userId: string, limit?: number): Promise<CampaignMessage[]>;
  getCampaignStats(campaignId: string): Promise<{
    total: number;
    sent: number;
    delivered: number;
    failed: number;
    pending: number;
  }>;

  // Dashboard stats
  getDashboardStats(userId: string): Promise<{
    totalCampaigns: number;
    messagesSent: number;
    successRate: number;
    activeCampaigns: number;
  }>;
}

export class DatabaseStorage implements IStorage {
  // User operations
  async getUser(id: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user;
  }

  async upsertUser(userData: UpsertUser): Promise<User> {
    const [user] = await db
      .insert(users)
      .values(userData)
      .onConflictDoUpdate({
        target: users.id,
        set: {
          ...userData,
          updatedAt: new Date(),
        },
      })
      .returning();
    return user;
  }

  // Evolution API operations
  async createEvolutionInstance(instance: InsertEvolutionInstance): Promise<EvolutionInstance> {
    const [newInstance] = await db.insert(evolutionInstances).values(instance).returning();
    return newInstance;
  }

  async getEvolutionInstances(userId: string): Promise<EvolutionInstance[]> {
    return await db.select().from(evolutionInstances).where(eq(evolutionInstances.userId, userId));
  }

  async getEvolutionInstance(id: string): Promise<EvolutionInstance | undefined> {
    const [instance] = await db.select().from(evolutionInstances).where(eq(evolutionInstances.id, id));
    return instance;
  }

  async updateEvolutionInstanceStatus(id: string, status: string): Promise<void> {
    await db.update(evolutionInstances).set({ status, updatedAt: new Date() }).where(eq(evolutionInstances.id, id));
  }

  async deleteEvolutionInstance(id: string): Promise<void> {
    await db.delete(evolutionInstances).where(eq(evolutionInstances.id, id));
  }

  // Contact operations
  async createContactGroup(group: InsertContactGroup): Promise<ContactGroup> {
    const [newGroup] = await db.insert(contactGroups).values(group).returning();
    return newGroup;
  }

  async getContactGroups(userId: string): Promise<ContactGroup[]> {
    return await db.select().from(contactGroups).where(eq(contactGroups.userId, userId));
  }

  async getContactGroup(id: string): Promise<ContactGroup | undefined> {
    const [group] = await db.select().from(contactGroups).where(eq(contactGroups.id, id));
    return group;
  }

  async updateContactGroup(id: string, data: Partial<InsertContactGroup>): Promise<ContactGroup> {
    const [updated] = await db.update(contactGroups).set({ ...data, updatedAt: new Date() }).where(eq(contactGroups.id, id)).returning();
    return updated;
  }

  async deleteContactGroup(id: string): Promise<void> {
    await db.delete(contactGroups).where(eq(contactGroups.id, id));
  }

  async createContact(contact: InsertContact): Promise<Contact> {
    const [newContact] = await db.insert(contacts).values(contact).returning();
    return newContact;
  }

  async getContacts(userId: string, groupId?: string): Promise<Contact[]> {
    const conditions = [eq(contacts.userId, userId)];
    if (groupId) {
      conditions.push(eq(contacts.groupId, groupId));
    }
    return await db.select().from(contacts).where(and(...conditions));
  }

  async getContact(id: string): Promise<Contact | undefined> {
    const [contact] = await db.select().from(contacts).where(eq(contacts.id, id));
    return contact;
  }

  async getContactByPhone(phoneNumber: string, userId: string): Promise<Contact | undefined> {
    const [contact] = await db.select().from(contacts).where(and(eq(contacts.phoneNumber, phoneNumber), eq(contacts.userId, userId)));
    return contact;
  }

  async updateContact(id: string, data: Partial<InsertContact>): Promise<Contact> {
    const [updated] = await db.update(contacts).set({ ...data, updatedAt: new Date() }).where(eq(contacts.id, id)).returning();
    return updated;
  }

  async deleteContact(id: string): Promise<void> {
    await db.delete(contacts).where(eq(contacts.id, id));
  }

  async bulkCreateContacts(contactList: InsertContact[]): Promise<Contact[]> {
    if (contactList.length === 0) return [];
    return await db.insert(contacts).values(contactList).returning();
  }

  // Template operations
  async createMessageTemplate(template: InsertMessageTemplate): Promise<MessageTemplate> {
    const [newTemplate] = await db.insert(messageTemplates).values(template).returning();
    return newTemplate;
  }

  async getMessageTemplates(userId: string): Promise<MessageTemplate[]> {
    return await db.select().from(messageTemplates).where(eq(messageTemplates.userId, userId));
  }

  async getMessageTemplate(id: string): Promise<MessageTemplate | undefined> {
    const [template] = await db.select().from(messageTemplates).where(eq(messageTemplates.id, id));
    return template;
  }

  async updateMessageTemplate(id: string, data: Partial<InsertMessageTemplate>): Promise<MessageTemplate> {
    const [updated] = await db.update(messageTemplates).set({ ...data, updatedAt: new Date() }).where(eq(messageTemplates.id, id)).returning();
    return updated;
  }

  async deleteMessageTemplate(id: string): Promise<void> {
    await db.delete(messageTemplates).where(eq(messageTemplates.id, id));
  }

  // Campaign operations
  async createCampaign(campaign: InsertCampaign): Promise<Campaign> {
    const [newCampaign] = await db.insert(campaigns).values(campaign).returning();
    return newCampaign;
  }

  async getCampaigns(userId: string): Promise<Campaign[]> {
    return await db.select().from(campaigns).where(eq(campaigns.userId, userId)).orderBy(desc(campaigns.createdAt));
  }

  async getCampaign(id: string): Promise<Campaign | undefined> {
    const [campaign] = await db.select().from(campaigns).where(eq(campaigns.id, id));
    return campaign;
  }

  async updateCampaign(id: string, data: Partial<InsertCampaign>): Promise<Campaign> {
    const [updated] = await db.update(campaigns).set({ ...data, updatedAt: new Date() }).where(eq(campaigns.id, id)).returning();
    return updated;
  }

  async deleteCampaign(id: string): Promise<void> {
    await db.delete(campaigns).where(eq(campaigns.id, id));
  }

  async getActiveCampaigns(userId: string): Promise<Campaign[]> {
    return await db.select().from(campaigns).where(and(eq(campaigns.userId, userId), sql`${campaigns.status} IN ('running', 'paused')`));
  }

  async getScheduledCampaigns(): Promise<Campaign[]> {
    return await db.select().from(campaigns).where(and(eq(campaigns.status, 'scheduled'), sql`${campaigns.scheduledAt} <= NOW()`));
  }

  // Campaign message operations
  async createCampaignMessage(message: InsertCampaignMessage): Promise<CampaignMessage> {
    const [newMessage] = await db.insert(campaignMessages).values(message).returning();
    return newMessage;
  }

  async getCampaignMessages(campaignId: string): Promise<CampaignMessage[]> {
    return await db.select().from(campaignMessages).where(eq(campaignMessages.campaignId, campaignId));
  }

  async getCampaignMessage(id: string): Promise<CampaignMessage | undefined> {
    const [message] = await db.select().from(campaignMessages).where(eq(campaignMessages.id, id));
    return message;
  }

  async updateCampaignMessage(id: string, data: Partial<InsertCampaignMessage>): Promise<CampaignMessage> {
    const [updated] = await db.update(campaignMessages).set({ ...data, updatedAt: new Date() }).where(eq(campaignMessages.id, id)).returning();
    return updated;
  }

  async bulkCreateCampaignMessages(messages: InsertCampaignMessage[]): Promise<CampaignMessage[]> {
    if (messages.length === 0) return [];
    return await db.insert(campaignMessages).values(messages).returning();
  }

  async getRecentMessages(userId: string, limit: number = 20): Promise<CampaignMessage[]> {
    return await db
      .select({
        id: campaignMessages.id,
        campaignId: campaignMessages.campaignId,
        contactId: campaignMessages.contactId,
        phoneNumber: campaignMessages.phoneNumber,
        messageContent: campaignMessages.messageContent,
        status: campaignMessages.status,
        scheduledAt: campaignMessages.scheduledAt,
        sentAt: campaignMessages.sentAt,
        deliveredAt: campaignMessages.deliveredAt,
        readAt: campaignMessages.readAt,
        errorMessage: campaignMessages.errorMessage,
        evolutionMessageId: campaignMessages.evolutionMessageId,
        createdAt: campaignMessages.createdAt,
        updatedAt: campaignMessages.updatedAt,
      })
      .from(campaignMessages)
      .innerJoin(campaigns, eq(campaignMessages.campaignId, campaigns.id))
      .where(eq(campaigns.userId, userId))
      .orderBy(desc(campaignMessages.createdAt))
      .limit(limit);
  }

  async getCampaignStats(campaignId: string): Promise<{
    total: number;
    sent: number;
    delivered: number;
    failed: number;
    pending: number;
  }> {
    const [stats] = await db
      .select({
        total: count(),
        sent: sql<number>`SUM(CASE WHEN ${campaignMessages.status} IN ('sent', 'delivered', 'read') THEN 1 ELSE 0 END)`,
        delivered: sql<number>`SUM(CASE WHEN ${campaignMessages.status} IN ('delivered', 'read') THEN 1 ELSE 0 END)`,
        failed: sql<number>`SUM(CASE WHEN ${campaignMessages.status} = 'failed' THEN 1 ELSE 0 END)`,
        pending: sql<number>`SUM(CASE WHEN ${campaignMessages.status} IN ('pending', 'sending') THEN 1 ELSE 0 END)`,
      })
      .from(campaignMessages)
      .where(eq(campaignMessages.campaignId, campaignId));

    return {
      total: Number(stats.total),
      sent: Number(stats.sent),
      delivered: Number(stats.delivered),
      failed: Number(stats.failed),
      pending: Number(stats.pending),
    };
  }

  async getDashboardStats(userId: string): Promise<{
    totalCampaigns: number;
    messagesSent: number;
    successRate: number;
    activeCampaigns: number;
  }> {
    const [campaignStats] = await db
      .select({
        totalCampaigns: count(),
        activeCampaigns: sql<number>`SUM(CASE WHEN ${campaigns.status} IN ('running', 'paused') THEN 1 ELSE 0 END)`,
      })
      .from(campaigns)
      .where(eq(campaigns.userId, userId));

    const [messageStats] = await db
      .select({
        messagesSent: sql<number>`SUM(${campaigns.sentCount})`,
        messagesDelivered: sql<number>`SUM(${campaigns.deliveredCount})`,
      })
      .from(campaigns)
      .where(eq(campaigns.userId, userId));

    const totalMessages = Number(messageStats.messagesSent) || 0;
    const deliveredMessages = Number(messageStats.messagesDelivered) || 0;
    const successRate = totalMessages > 0 ? (deliveredMessages / totalMessages) * 100 : 0;

    return {
      totalCampaigns: Number(campaignStats.totalCampaigns),
      messagesSent: totalMessages,
      successRate: Math.round(successRate * 100) / 100,
      activeCampaigns: Number(campaignStats.activeCampaigns),
    };
  }
}

export const storage = new DatabaseStorage();
